{"tests/test_config.py::TestConfigIntegration": true, "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_valid": true, "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_invalid": true, "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_valid": true, "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_invalid": true, "tests/test_utils.py::TestFormattingFunctions::test_format_card_counts": true, "tests/test_utils.py::TestFormattingFunctions::test_format_duration": true, "tests/test_utils.py::TestFormattingFunctions::test_format_datetime": true, "tests/test_utils.py::TestFormattingFunctions::test_truncate_text": true, "tests/test_utils.py::TestFileOperations::test_parse_csv_file_valid": true, "tests/test_utils.py::TestFileOperations::test_parse_csv_file_with_delimiter": true, "tests/test_utils.py::TestFileOperations::test_parse_csv_file_with_quotes": true, "tests/test_utils.py::TestFileOperations::test_parse_csv_file_invalid": true, "tests/test_utils.py::TestFileOperations::test_sanitize_filename": true, "tests/test_config.py::TestConfigIntegration::test_config_precedence": true, "tests/test_config.py::TestConfigIntegration::test_config_partial_file": true, "tests/test_init.py::TestInitialization::test_initialize_srs_with_config_file": true, "tests/test_init.py::TestInitialization::test_initialize_srs_with_environment_variables": true, "tests/test_init.py::TestInitialization::test_initialize_srs_precedence": true, "tests/test_init.py::TestLoggingSetup::test_setup_logging_default": true, "tests/test_init.py::TestLoggingSetup::test_setup_logging_with_file": true, "tests/test_init.py::TestLoggingSetup::test_setup_logging_debug_level": true, "tests/test_init.py::TestLoggingSetup::test_setup_logging_creates_log_directory": true, "tests/test_init.py::TestPackageImports::test_main_imports": true, "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_permission_error": true, "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_disk_full": true, "tests/test_tags.py::TestCardTagRelationships::test_card_tag_database_operations": true}