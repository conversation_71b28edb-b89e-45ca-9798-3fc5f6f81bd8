["tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_get_algorithm_singleton", "tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_get_algorithm_with_custom_config", "tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_reset_algorithm", "tests/test_algorithm.py::TestSM2Algorithm::test_algorithm_initialization", "tests/test_algorithm.py::TestSM2Algorithm::test_algorithm_initialization_with_custom_config", "tests/test_algorithm.py::TestSM2Algorithm::test_due_date_calculation", "tests/test_algorithm.py::TestSM2Algorithm::test_ease_factor_adjustments", "tests/test_algorithm.py::TestSM2Algorithm::test_easy_bonus_calculation", "tests/test_algorithm.py::TestSM2Algorithm::test_fuzz_factor_application", "tests/test_algorithm.py::TestSM2Algorithm::test_interval_bounds_enforcement", "tests/test_algorithm.py::TestSM2Algorithm::test_invalid_rating_raises_error", "tests/test_algorithm.py::TestSM2Algorithm::test_new_card_learning_failure", "tests/test_algorithm.py::TestSM2Algorithm::test_new_card_learning_steps_progression", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_1", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_2", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_3", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_4", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_high_ease_factor", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_high_repetitions", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_low_ease_factor", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_zero_interval_card", "tests/test_algorithm.py::TestSM2Config::test_custom_config", "tests/test_algorithm.py::TestSM2Config::test_default_config", "tests/test_cli.py::TestArgumentParser::test_command_parsing", "tests/test_cli.py::TestArgumentParser::test_help_argument", "tests/test_cli.py::TestArgumentParser::test_invalid_command", "tests/test_cli.py::TestArgumentParser::test_parser_setup", "tests/test_cli.py::TestArgumentParser::test_version_argument", "tests/test_cli.py::TestCLICommands::test_add_card_invalid_content", "tests/test_cli.py::TestCLICommands::test_add_card_nonexistent_deck", "tests/test_cli.py::TestCLICommands::test_add_card_with_arguments", "tests/test_cli.py::TestCLICommands::test_create_deck_duplicate", "tests/test_cli.py::TestCLICommands::test_create_deck_invalid_name", "tests/test_cli.py::TestCLICommands::test_create_deck_success", "tests/test_cli.py::TestCLICommands::test_delete_deck_nonexistent", "tests/test_cli.py::TestCLICommands::test_delete_deck_success", "tests/test_cli.py::TestCLICommands::test_list_cards_due_only", "tests/test_cli.py::TestCLICommands::test_list_cards_empty_deck", "tests/test_cli.py::TestCLICommands::test_list_cards_with_cards", "tests/test_cli.py::TestCLICommands::test_list_cards_with_limit", "tests/test_cli.py::TestCLICommands::test_list_decks_detailed", "tests/test_cli.py::TestCLICommands::test_list_decks_empty", "tests/test_cli.py::TestCLICommands::test_list_decks_with_decks", "tests/test_cli.py::TestCLICommands::test_status_detailed", "tests/test_cli.py::TestCLICommands::test_status_empty", "tests/test_cli.py::TestCLICommands::test_status_with_decks", "tests/test_cli.py::TestCLIErrorHandling::test_initialization_failure", "tests/test_cli.py::TestCLIErrorHandling::test_invalid_command", "tests/test_cli.py::TestCLIErrorHandling::test_no_command", "tests/test_database.py::TestDatabase::test_backup_restore_functionality", "tests/test_database.py::TestDatabase::test_concurrent_access_safety", "tests/test_database.py::TestDatabase::test_data_persistence_between_sessions", "tests/test_database.py::TestDatabase::test_database_corruption_handling", "tests/test_database.py::TestDatabase::test_database_creation_on_first_run", "tests/test_database.py::TestDatabase::test_foreign_key_constraints", "tests/test_database.py::TestDatabase::test_indexes_creation", "tests/test_database.py::TestDatabase::test_query_timeout", "tests/test_database.py::TestDatabase::test_row_factory_configuration", "tests/test_database.py::TestDatabase::test_schema_creation", "tests/test_database.py::TestDatabase::test_transaction_rollback", "tests/test_database.py::TestDatabaseGlobalInstance::test_get_database_singleton", "tests/test_database.py::TestDatabaseGlobalInstance::test_get_database_with_custom_path", "tests/test_database.py::TestDatabaseGlobalInstance::test_reset_database", "tests/test_database.py::TestDatabasePerformance::test_database_operation_performance", "tests/test_database.py::TestDatabasePerformance::test_large_dataset_handling", "tests/test_models.py::TestCard::test_card_content_trimming", "tests/test_models.py::TestCard::test_card_creation_with_invalid_data", "tests/test_models.py::TestCard::test_card_creation_with_valid_data", "tests/test_models.py::TestCard::test_card_save_functionality", "tests/test_models.py::TestCard::test_get_all_due_cards", "tests/test_models.py::TestCard::test_get_card_by_id", "tests/test_models.py::TestCard::test_get_cards_by_deck", "tests/test_models.py::TestCard::test_get_due_cards", "tests/test_models.py::TestCard::test_unicode_support_in_card_content", "tests/test_models.py::TestDeck::test_deck_card_counts", "tests/test_models.py::TestDeck::test_deck_creation_with_invalid_names", "tests/test_models.py::TestDeck::test_deck_creation_with_valid_name", "tests/test_models.py::TestDeck::test_deck_deletion", "tests/test_models.py::TestDeck::test_deck_deletion_cascades_to_cards", "tests/test_models.py::TestDeck::test_deck_name_trimming", "tests/test_models.py::TestDeck::test_duplicate_deck_handling", "tests/test_models.py::TestDeck::test_get_all_decks", "tests/test_models.py::TestDeck::test_get_deck_by_id", "tests/test_models.py::TestDeck::test_get_deck_by_name", "tests/test_models.py::TestDeck::test_get_nonexistent_deck", "tests/test_models.py::TestModelRelationships::test_card_review_relationship", "tests/test_models.py::TestModelRelationships::test_cascading_deletes", "tests/test_models.py::TestModelRelationships::test_deck_card_relationship", "tests/test_models.py::TestReview::test_get_reviews_by_card", "tests/test_models.py::TestReview::test_review_creation_with_all_valid_ratings", "tests/test_models.py::TestReview::test_review_creation_with_invalid_rating", "tests/test_models.py::TestReview::test_review_creation_with_valid_data", "tests/test_models.py::TestReview::test_review_history_tracking", "tests/test_project_structure.py::TestConfiguration::test_config_manager", "tests/test_project_structure.py::TestConfiguration::test_default_config", "tests/test_project_structure.py::TestLogging::test_logging_setup", "tests/test_project_structure.py::TestProjectStructure::test_algorithm_module_import", "tests/test_project_structure.py::TestProjectStructure::test_cli_module_import", "tests/test_project_structure.py::TestProjectStructure::test_config_module_import", "tests/test_project_structure.py::TestProjectStructure::test_database_module_import", "tests/test_project_structure.py::TestProjectStructure::test_main_imports", "tests/test_project_structure.py::TestProjectStructure::test_models_module_import", "tests/test_project_structure.py::TestProjectStructure::test_review_module_import", "tests/test_project_structure.py::TestProjectStructure::test_srs_package_import", "tests/test_project_structure.py::TestProjectStructure::test_utils_module_import", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_nonexistent_deck", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_success", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_with_specific_cards", "tests/test_review.py::TestReviewSession::test_card_ordering_by_priority", "tests/test_review.py::TestReviewSession::test_end_session", "tests/test_review.py::TestReviewSession::test_progress_tracking", "tests/test_review.py::TestReviewSession::test_review_current_card_no_cards", "tests/test_review.py::TestReviewSession::test_review_current_card_success", "tests/test_review.py::TestReviewSession::test_session_completion", "tests/test_review.py::TestReviewSession::test_session_creation_with_due_cards", "tests/test_review.py::TestReviewSession::test_session_creation_with_no_cards", "tests/test_review.py::TestReviewSession::test_session_creation_with_specific_cards", "tests/test_review.py::TestReviewSession::test_session_interruption_and_resumption", "tests/test_review.py::TestReviewSession::test_session_state_restoration", "tests/test_review.py::TestReviewSession::test_session_state_saving", "tests/test_review.py::TestReviewSession::test_session_summary", "tests/test_review.py::TestReviewSession::test_skip_current_card", "tests/test_review.py::TestReviewSession::test_skip_current_card_no_cards", "tests/test_review.py::TestReviewSessionPerformance::test_large_session_creation_performance", "tests/test_review.py::TestReviewSessionPerformance::test_review_session_memory_usage", "tests/test_review.py::TestSessionStats::test_completion_percentage_calculation", "tests/test_review.py::TestSessionStats::test_duration_calculation", "tests/test_review.py::TestSessionStats::test_session_stats_initialization"]