["tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_get_algorithm_singleton", "tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_get_algorithm_with_custom_config", "tests/test_algorithm.py::TestGlobalAlgorithmInstance::test_reset_algorithm", "tests/test_algorithm.py::TestSM2Algorithm::test_algorithm_initialization", "tests/test_algorithm.py::TestSM2Algorithm::test_algorithm_initialization_with_custom_config", "tests/test_algorithm.py::TestSM2Algorithm::test_due_date_calculation", "tests/test_algorithm.py::TestSM2Algorithm::test_ease_factor_adjustments", "tests/test_algorithm.py::TestSM2Algorithm::test_easy_bonus_calculation", "tests/test_algorithm.py::TestSM2Algorithm::test_fuzz_factor_application", "tests/test_algorithm.py::TestSM2Algorithm::test_interval_bounds_enforcement", "tests/test_algorithm.py::TestSM2Algorithm::test_invalid_rating_raises_error", "tests/test_algorithm.py::TestSM2Algorithm::test_new_card_learning_failure", "tests/test_algorithm.py::TestSM2Algorithm::test_new_card_learning_steps_progression", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_1", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_2", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_3", "tests/test_algorithm.py::TestSM2Algorithm::test_sm2_interval_calculation_rating_4", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_high_ease_factor", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_high_repetitions", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_very_low_ease_factor", "tests/test_algorithm.py::TestSM2AlgorithmEdgeCases::test_zero_interval_card", "tests/test_algorithm.py::TestSM2Config::test_custom_config", "tests/test_algorithm.py::TestSM2Config::test_default_config", "tests/test_cli.py::TestArgumentParser::test_command_parsing", "tests/test_cli.py::TestArgumentParser::test_help_argument", "tests/test_cli.py::TestArgumentParser::test_invalid_command", "tests/test_cli.py::TestArgumentParser::test_parser_setup", "tests/test_cli.py::TestArgumentParser::test_version_argument", "tests/test_cli.py::TestCLICommands::test_add_card_invalid_content", "tests/test_cli.py::TestCLICommands::test_add_card_nonexistent_deck", "tests/test_cli.py::TestCLICommands::test_add_card_with_arguments", "tests/test_cli.py::TestCLICommands::test_create_deck_duplicate", "tests/test_cli.py::TestCLICommands::test_create_deck_invalid_name", "tests/test_cli.py::TestCLICommands::test_create_deck_success", "tests/test_cli.py::TestCLICommands::test_delete_deck_nonexistent", "tests/test_cli.py::TestCLICommands::test_delete_deck_success", "tests/test_cli.py::TestCLICommands::test_list_cards_due_only", "tests/test_cli.py::TestCLICommands::test_list_cards_empty_deck", "tests/test_cli.py::TestCLICommands::test_list_cards_with_cards", "tests/test_cli.py::TestCLICommands::test_list_cards_with_limit", "tests/test_cli.py::TestCLICommands::test_list_decks_detailed", "tests/test_cli.py::TestCLICommands::test_list_decks_empty", "tests/test_cli.py::TestCLICommands::test_list_decks_with_decks", "tests/test_cli.py::TestCLICommands::test_status_detailed", "tests/test_cli.py::TestCLICommands::test_status_empty", "tests/test_cli.py::TestCLICommands::test_status_with_decks", "tests/test_cli.py::TestCLIEdgeCases::test_command_with_unicode_arguments", "tests/test_cli.py::TestCLIEdgeCases::test_empty_arguments", "tests/test_cli.py::TestCLIEdgeCases::test_keyboard_interrupt_handling", "tests/test_cli.py::TestCLIEdgeCases::test_unexpected_exception_handling", "tests/test_cli.py::TestCLIEdgeCases::test_very_long_arguments", "tests/test_cli.py::TestCLIErrorHandling::test_initialization_failure", "tests/test_cli.py::TestCLIErrorHandling::test_invalid_command", "tests/test_cli.py::TestCLIErrorHandling::test_no_command", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_add_card_interactive_mode", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_config_file_argument", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_delete_deck_cancelled", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_delete_deck_with_confirmation", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_import_cards_actual", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_import_cards_dry_run", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_import_cards_file_not_found", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_review_command_no_due_cards", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_review_command_with_limit", "tests/test_cli.py::TestCLIInteractiveFunctionality::test_verbose_flag", "tests/test_config.py::TestConfigBasic::test_config_file_corruption_handling", "tests/test_config.py::TestConfigBasic::test_config_partial_file", "tests/test_config.py::TestConfigFileOperations::test_load_config_from_file", "tests/test_config.py::TestConfigFileOperations::test_load_config_nonexistent_file", "tests/test_config.py::TestConfigFileOperations::test_save_config", "tests/test_config.py::TestConfigFunctions::test_get_config", "tests/test_config.py::TestConfigFunctions::test_get_config_manager", "tests/test_config.py::TestConfigFunctions::test_reset_config", "tests/test_config.py::TestConfigFunctions::test_setup_logging", "tests/test_config.py::TestConfigIntegration::test_config_file_corruption_handling", "tests/test_config.py::TestConfigIntegration::test_config_partial_file", "tests/test_config.py::TestConfigIntegration::test_config_precedence", "tests/test_config.py::TestSRSConfig::test_config_attributes", "tests/test_config.py::TestSRSConfig::test_config_initialization_custom", "tests/test_config.py::TestSRSConfig::test_config_initialization_defaults", "tests/test_config.py::TestSRSConfig::test_config_path_expansion", "tests/test_config.py::TestSRSConfig::test_config_string_representation", "tests/test_config.py::TestSRSConfig::test_config_validation_basic", "tests/test_database.py::TestDatabase::test_backup_restore_functionality", "tests/test_database.py::TestDatabase::test_concurrent_access_safety", "tests/test_database.py::TestDatabase::test_data_persistence_between_sessions", "tests/test_database.py::TestDatabase::test_database_corruption_handling", "tests/test_database.py::TestDatabase::test_database_creation_on_first_run", "tests/test_database.py::TestDatabase::test_foreign_key_constraints", "tests/test_database.py::TestDatabase::test_indexes_creation", "tests/test_database.py::TestDatabase::test_query_timeout", "tests/test_database.py::TestDatabase::test_row_factory_configuration", "tests/test_database.py::TestDatabase::test_schema_creation", "tests/test_database.py::TestDatabase::test_transaction_rollback", "tests/test_database.py::TestDatabaseGlobalInstance::test_get_database_singleton", "tests/test_database.py::TestDatabaseGlobalInstance::test_get_database_with_custom_path", "tests/test_database.py::TestDatabaseGlobalInstance::test_reset_database", "tests/test_database.py::TestDatabasePerformance::test_database_operation_performance", "tests/test_database.py::TestDatabasePerformance::test_large_dataset_handling", "tests/test_init.py::TestInitialization::test_initialize_srs_creates_directories", "tests/test_init.py::TestInitialization::test_initialize_srs_default", "tests/test_init.py::TestInitialization::test_initialize_srs_precedence", "tests/test_init.py::TestInitialization::test_initialize_srs_with_config_file", "tests/test_init.py::TestInitialization::test_initialize_srs_with_environment_variables", "tests/test_init.py::TestInitialization::test_initialize_srs_with_invalid_config", "tests/test_init.py::TestInitialization::test_initialize_srs_with_nonexistent_config", "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_corrupted_database", "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_disk_full", "tests/test_init.py::TestInitializationEdgeCases::test_initialize_with_permission_error", "tests/test_init.py::TestLoggingSetup::test_setup_logging_creates_log_directory", "tests/test_init.py::TestLoggingSetup::test_setup_logging_debug_level", "tests/test_init.py::TestLoggingSetup::test_setup_logging_default", "tests/test_init.py::TestLoggingSetup::test_setup_logging_with_file", "tests/test_init.py::TestPackageImports::test_circular_imports", "tests/test_init.py::TestPackageImports::test_main_imports", "tests/test_init.py::TestSystemIntegration::test_full_initialization_workflow", "tests/test_init.py::TestSystemIntegration::test_initialization_thread_safety", "tests/test_init.py::TestSystemIntegration::test_multiple_initialization_calls", "tests/test_init.py::TestVersionInfo::test_version_exists", "tests/test_init.py::TestVersionInfo::test_version_format", "tests/test_models.py::TestCard::test_card_content_trimming", "tests/test_models.py::TestCard::test_card_creation_with_invalid_data", "tests/test_models.py::TestCard::test_card_creation_with_valid_data", "tests/test_models.py::TestCard::test_card_save_functionality", "tests/test_models.py::TestCard::test_get_all_due_cards", "tests/test_models.py::TestCard::test_get_card_by_id", "tests/test_models.py::TestCard::test_get_cards_by_deck", "tests/test_models.py::TestCard::test_get_due_cards", "tests/test_models.py::TestCard::test_unicode_support_in_card_content", "tests/test_models.py::TestDeck::test_deck_card_counts", "tests/test_models.py::TestDeck::test_deck_creation_with_invalid_names", "tests/test_models.py::TestDeck::test_deck_creation_with_valid_name", "tests/test_models.py::TestDeck::test_deck_deletion", "tests/test_models.py::TestDeck::test_deck_deletion_cascades_to_cards", "tests/test_models.py::TestDeck::test_deck_name_trimming", "tests/test_models.py::TestDeck::test_duplicate_deck_handling", "tests/test_models.py::TestDeck::test_get_all_decks", "tests/test_models.py::TestDeck::test_get_deck_by_id", "tests/test_models.py::TestDeck::test_get_deck_by_name", "tests/test_models.py::TestDeck::test_get_nonexistent_deck", "tests/test_models.py::TestModelRelationships::test_card_review_relationship", "tests/test_models.py::TestModelRelationships::test_cascading_deletes", "tests/test_models.py::TestModelRelationships::test_deck_card_relationship", "tests/test_models.py::TestReview::test_get_reviews_by_card", "tests/test_models.py::TestReview::test_review_creation_with_all_valid_ratings", "tests/test_models.py::TestReview::test_review_creation_with_invalid_rating", "tests/test_models.py::TestReview::test_review_creation_with_valid_data", "tests/test_models.py::TestReview::test_review_history_tracking", "tests/test_project_structure.py::TestConfiguration::test_config_manager", "tests/test_project_structure.py::TestConfiguration::test_default_config", "tests/test_project_structure.py::TestLogging::test_logging_setup", "tests/test_project_structure.py::TestProjectStructure::test_algorithm_module_import", "tests/test_project_structure.py::TestProjectStructure::test_cli_module_import", "tests/test_project_structure.py::TestProjectStructure::test_config_module_import", "tests/test_project_structure.py::TestProjectStructure::test_database_module_import", "tests/test_project_structure.py::TestProjectStructure::test_main_imports", "tests/test_project_structure.py::TestProjectStructure::test_models_module_import", "tests/test_project_structure.py::TestProjectStructure::test_review_module_import", "tests/test_project_structure.py::TestProjectStructure::test_srs_package_import", "tests/test_project_structure.py::TestProjectStructure::test_utils_module_import", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_nonexistent_deck", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_success", "tests/test_review.py::TestCreateReviewSession::test_create_review_session_with_specific_cards", "tests/test_review.py::TestReviewSession::test_card_ordering_by_priority", "tests/test_review.py::TestReviewSession::test_end_session", "tests/test_review.py::TestReviewSession::test_progress_tracking", "tests/test_review.py::TestReviewSession::test_review_current_card_no_cards", "tests/test_review.py::TestReviewSession::test_review_current_card_success", "tests/test_review.py::TestReviewSession::test_session_completion", "tests/test_review.py::TestReviewSession::test_session_creation_with_due_cards", "tests/test_review.py::TestReviewSession::test_session_creation_with_no_cards", "tests/test_review.py::TestReviewSession::test_session_creation_with_specific_cards", "tests/test_review.py::TestReviewSession::test_session_interruption_and_resumption", "tests/test_review.py::TestReviewSession::test_session_state_restoration", "tests/test_review.py::TestReviewSession::test_session_state_saving", "tests/test_review.py::TestReviewSession::test_session_summary", "tests/test_review.py::TestReviewSession::test_skip_current_card", "tests/test_review.py::TestReviewSession::test_skip_current_card_no_cards", "tests/test_review.py::TestReviewSessionPerformance::test_large_session_creation_performance", "tests/test_review.py::TestReviewSessionPerformance::test_review_session_memory_usage", "tests/test_review.py::TestSessionStats::test_completion_percentage_calculation", "tests/test_review.py::TestSessionStats::test_duration_calculation", "tests/test_review.py::TestSessionStats::test_session_stats_initialization", "tests/test_utils.py::TestFileOperations::test_ensure_directory", "tests/test_utils.py::TestFileOperations::test_ensure_directory_basic", "tests/test_utils.py::TestFileOperations::test_get_file_size_mb", "tests/test_utils.py::TestFileOperations::test_get_file_size_mb_basic", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_basic", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_invalid", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_nonexistent", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_valid", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_with_delimiter", "tests/test_utils.py::TestFileOperations::test_parse_csv_file_with_quotes", "tests/test_utils.py::TestFileOperations::test_sanitize_filename", "tests/test_utils.py::TestFileOperations::test_sanitize_filename_basic", "tests/test_utils.py::TestFormattingFunctions::test_format_card_counts", "tests/test_utils.py::TestFormattingFunctions::test_format_card_counts_basic", "tests/test_utils.py::TestFormattingFunctions::test_format_datetime", "tests/test_utils.py::TestFormattingFunctions::test_format_datetime_basic", "tests/test_utils.py::TestFormattingFunctions::test_format_duration", "tests/test_utils.py::TestFormattingFunctions::test_format_duration_basic", "tests/test_utils.py::TestFormattingFunctions::test_truncate_text", "tests/test_utils.py::TestFormattingFunctions::test_truncate_text_basic", "tests/test_utils.py::TestTextProcessing::test_clean_text", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_basic", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_edge_cases", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_invalid", "tests/test_utils.py::TestValidationFunctions::test_validate_card_content_valid", "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_basic", "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_invalid", "tests/test_utils.py::TestValidationFunctions::test_validate_deck_name_valid"]