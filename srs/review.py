"""
Review session management for the spaced repetition system.

This module handles review session logic, card ordering, progress tracking,
and session state management.
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field

from .models import Card, Review, Deck
from .algorithm import get_algorithm

logger = logging.getLogger(__name__)


@dataclass
class SessionStats:
    """Statistics for a review session."""
    total_cards: int = 0
    cards_reviewed: int = 0
    cards_remaining: int = 0
    rating_counts: Dict[int, int] = field(default_factory=lambda: {1: 0, 2: 0, 3: 0, 4: 0})
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    @property
    def duration_minutes(self) -> float:
        """Get session duration in minutes."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds() / 60
        return 0.0
    
    @property
    def completion_percentage(self) -> float:
        """Get completion percentage."""
        if self.total_cards == 0:
            return 100.0
        return (self.cards_reviewed / self.total_cards) * 100


class ReviewSession:
    """
    Manages a review session for a deck or set of cards.
    
    Handles card ordering, progress tracking, session interruption/resumption,
    and statistics collection.
    """
    
    def __init__(self, deck: Deck, cards: Optional[List[Card]] = None):
        """
        Initialize a review session.
        
        Args:
            deck: The deck being reviewed
            cards: Optional list of specific cards to review
        """
        self.deck = deck
        self.algorithm = get_algorithm()
        self.stats = SessionStats()
        
        # Get cards to review
        if cards is not None:
            self.cards = cards.copy()
        else:
            self.cards = Card.get_due_cards(deck.id)
        
        # Order cards by priority (due cards first, then by due date)
        self.cards = self._order_cards(self.cards)
        
        self.current_index = 0
        self.stats.total_cards = len(self.cards)
        self.stats.cards_remaining = len(self.cards)
        self.stats.start_time = datetime.now()
        
        logger.info(f"Started review session for deck '{deck.name}' with {len(self.cards)} cards")
    
    def _order_cards(self, cards: List[Card]) -> List[Card]:
        """
        Order cards by review priority.
        
        Priority order:
        1. Due cards (due_date <= now) ordered by due_date (oldest first)
        2. New cards (repetitions = 0) ordered by creation date
        3. Future cards ordered by due_date
        """
        now = datetime.now()
        
        # Separate cards by type
        due_cards = [c for c in cards if c.due_date <= now and c.repetitions > 0]
        new_cards = [c for c in cards if c.repetitions == 0]
        future_cards = [c for c in cards if c.due_date > now and c.repetitions > 0]
        
        # Sort each category
        due_cards.sort(key=lambda c: c.due_date)
        new_cards.sort(key=lambda c: c.created_at or datetime.now())
        future_cards.sort(key=lambda c: c.due_date)
        
        # Combine in priority order
        ordered_cards = due_cards + new_cards + future_cards
        
        logger.debug(f"Ordered cards: {len(due_cards)} due, {len(new_cards)} new, {len(future_cards)} future")
        return ordered_cards
    
    @property
    def has_cards(self) -> bool:
        """Check if there are cards remaining in the session."""
        return self.current_index < len(self.cards)
    
    @property
    def current_card(self) -> Optional[Card]:
        """Get the current card to review."""
        if self.has_cards:
            return self.cards[self.current_index]
        return None
    
    @property
    def progress(self) -> Dict[str, Any]:
        """Get current session progress information."""
        return {
            'current_position': self.current_index + 1 if self.has_cards else self.stats.total_cards,
            'total_cards': self.stats.total_cards,
            'cards_reviewed': self.stats.cards_reviewed,
            'cards_remaining': self.stats.cards_remaining,
            'completion_percentage': self.stats.completion_percentage,
            'deck_name': self.deck.name
        }
    
    def review_current_card(self, rating: int) -> bool:
        """
        Review the current card with the given rating.
        
        Args:
            rating: User rating (1-4)
            
        Returns:
            True if review was successful, False if no current card
        """
        if not self.has_cards:
            logger.warning("Attempted to review card but no cards remaining")
            return False
        
        card = self.current_card
        if not card:
            return False
        
        try:
            # Apply SM-2 algorithm to get new scheduling
            result = self.algorithm.review_card(card, rating)

            # Update card with new scheduling parameters
            card.interval = result.interval
            card.repetitions = result.repetitions
            card.ease_factor = result.ease_factor
            card.due_date = result.due_date

            # Update difficulty tracking (Phase 1.1.2)
            # TODO: Add review duration tracking in future
            card.update_difficulty(rating, review_duration=0)

            # Save card changes to database
            card.save()
            
            # Create review record
            Review.create(card.id, rating)
            
            # Update session statistics
            self.stats.cards_reviewed += 1
            self.stats.cards_remaining -= 1
            self.stats.rating_counts[rating] += 1
            
            # Move to next card
            self.current_index += 1
            
            logger.info(f"Reviewed card {card.id} with rating {rating}, next due: {result.due_date}")
            return True
            
        except Exception as e:
            logger.error(f"Error reviewing card {card.id}: {e}")
            return False
    
    def skip_current_card(self) -> bool:
        """
        Skip the current card without reviewing it.
        
        Returns:
            True if skip was successful, False if no current card
        """
        if not self.has_cards:
            return False
        
        logger.info(f"Skipped card {self.current_card.id}")
        self.current_index += 1
        self.stats.cards_remaining -= 1
        return True
    
    def end_session(self):
        """End the review session and finalize statistics."""
        self.stats.end_time = datetime.now()
        
        logger.info(f"Ended review session for deck '{self.deck.name}'")
        logger.info(f"Session stats: {self.stats.cards_reviewed}/{self.stats.total_cards} cards reviewed")
        logger.info(f"Duration: {self.stats.duration_minutes:.1f} minutes")
        logger.info(f"Ratings: {self.stats.rating_counts}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the completed session.
        
        Returns:
            Dictionary with session statistics and summary
        """
        if not self.stats.end_time:
            self.end_session()
        
        return {
            'deck_name': self.deck.name,
            'total_cards': self.stats.total_cards,
            'cards_reviewed': self.stats.cards_reviewed,
            'completion_percentage': self.stats.completion_percentage,
            'duration_minutes': self.stats.duration_minutes,
            'rating_counts': self.stats.rating_counts.copy(),
            'average_rating': self._calculate_average_rating(),
            'start_time': self.stats.start_time,
            'end_time': self.stats.end_time
        }
    
    def _calculate_average_rating(self) -> float:
        """Calculate the average rating for the session."""
        total_ratings = sum(self.stats.rating_counts.values())
        if total_ratings == 0:
            return 0.0
        
        weighted_sum = sum(rating * count for rating, count in self.stats.rating_counts.items())
        return weighted_sum / total_ratings
    
    def save_state(self) -> Dict[str, Any]:
        """
        Save the current session state for resumption.
        
        Returns:
            Dictionary with session state data
        """
        return {
            'deck_id': self.deck.id,
            'current_index': self.current_index,
            'card_ids': [card.id for card in self.cards],
            'stats': {
                'total_cards': self.stats.total_cards,
                'cards_reviewed': self.stats.cards_reviewed,
                'cards_remaining': self.stats.cards_remaining,
                'rating_counts': self.stats.rating_counts.copy(),
                'start_time': self.stats.start_time.isoformat() if self.stats.start_time else None
            }
        }
    
    @classmethod
    def restore_from_state(cls, state: Dict[str, Any]) -> 'ReviewSession':
        """
        Restore a review session from saved state.
        
        Args:
            state: Previously saved session state
            
        Returns:
            Restored ReviewSession instance
        """
        # Get deck
        deck = Deck.get_by_id(state['deck_id'])
        if not deck:
            raise ValueError(f"Deck {state['deck_id']} not found")
        
        # Get cards by IDs
        cards = []
        for card_id in state['card_ids']:
            card = Card.get_by_id(card_id)
            if card:
                cards.append(card)
        
        # Create session
        session = cls(deck, cards)
        
        # Restore state
        session.current_index = state['current_index']
        session.stats.total_cards = state['stats']['total_cards']
        session.stats.cards_reviewed = state['stats']['cards_reviewed']
        session.stats.cards_remaining = state['stats']['cards_remaining']
        session.stats.rating_counts = state['stats']['rating_counts']
        
        if state['stats']['start_time']:
            session.stats.start_time = datetime.fromisoformat(state['stats']['start_time'])
        
        logger.info(f"Restored review session for deck '{deck.name}' at position {session.current_index}")
        return session


def create_review_session(deck_name: str, cards: Optional[List[Card]] = None) -> Optional[ReviewSession]:
    """
    Create a new review session for a deck.
    
    Args:
        deck_name: Name of the deck to review
        cards: Optional specific cards to review
        
    Returns:
        ReviewSession instance or None if deck not found
    """
    deck = Deck.get_by_name(deck_name)
    if not deck:
        logger.error(f"Deck '{deck_name}' not found")
        return None
    
    return ReviewSession(deck, cards)
