"""
Database layer for the spaced repetition system.

This module handles SQLite database operations, schema creation,
and connection management for the SRS application.
"""

import sqlite3
import logging
import threading
from pathlib import Path
from typing import Optional, List
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class Database:
    """
    SQLite database manager for the spaced repetition system.
    
    Handles database connection, schema creation, and basic operations
    with thread safety and proper error handling.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize database connection.
        
        Args:
            db_path: Path to SQLite database file. If None, uses default location.
        """
        self.db_path = db_path or self._get_default_db_path()
        self._local = threading.local()
        self._ensure_database_exists()
        
    def _get_default_db_path(self) -> str:
        """Get the default database path (~/.srs/data.db)."""
        srs_dir = Path.home() / ".srs"
        srs_dir.mkdir(exist_ok=True)
        return str(srs_dir / "data.db")
    
    def _ensure_database_exists(self):
        """Ensure database file exists and create schema if needed."""
        db_file = Path(self.db_path)
        db_file.parent.mkdir(parents=True, exist_ok=True)

        # Check if database is corrupted or needs initialization
        needs_init = False
        if not db_file.exists() or db_file.stat().st_size == 0:
            needs_init = True
            logger.info(f"Creating new database at {self.db_path}")
        else:
            # Test if database is valid
            try:
                test_conn = sqlite3.connect(self.db_path)
                test_conn.execute("SELECT name FROM sqlite_master LIMIT 1")
                test_conn.close()
                logger.info(f"Using existing database at {self.db_path}")
            except sqlite3.DatabaseError as e:
                logger.error(f"Database corruption detected: {e}")
                raise sqlite3.DatabaseError(f"Database file is corrupted: {self.db_path}")

        if needs_init:
            self._create_schema()
    
    @property
    def connection(self) -> sqlite3.Connection:
        """Get thread-local database connection."""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # Enable foreign key constraints
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            
        return self._local.connection
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions."""
        conn = self.connection
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def _create_schema(self):
        """Create database schema with tables and indexes."""
        schema_sql = """
        -- Core tables for MVP
        CREATE TABLE IF NOT EXISTS decks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS cards (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            deck_id INTEGER NOT NULL,
            front TEXT NOT NULL,
            back TEXT NOT NULL,
            interval INTEGER DEFAULT 0,
            repetitions INTEGER DEFAULT 0,
            ease_factor REAL DEFAULT 2.5,
            due_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (deck_id) REFERENCES decks(id) ON DELETE CASCADE
        );

        CREATE TABLE IF NOT EXISTS reviews (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            card_id INTEGER NOT NULL,
            rating INTEGER NOT NULL,
            reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (card_id) REFERENCES cards(id) ON DELETE CASCADE
        );

        -- Performance optimization index
        CREATE INDEX IF NOT EXISTS idx_due_cards ON cards(due_date, deck_id);
        CREATE INDEX IF NOT EXISTS idx_deck_cards ON cards(deck_id);
        CREATE INDEX IF NOT EXISTS idx_card_reviews ON reviews(card_id);
        """
        
        with self.transaction() as conn:
            conn.executescript(schema_sql)
        
        logger.info("Database schema created successfully")
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """
        Execute a SELECT query and return results.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of result rows
        """
        with self.connection as conn:
            cursor = conn.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        Execute an INSERT/UPDATE/DELETE query.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Number of affected rows or last row ID for INSERT
        """
        with self.transaction() as conn:
            cursor = conn.execute(query, params)
            return cursor.lastrowid if query.strip().upper().startswith('INSERT') else cursor.rowcount
    
    def close(self):
        """Close database connection."""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


# Global database instance
_db_instance: Optional[Database] = None


def get_database(db_path: Optional[str] = None) -> Database:
    """
    Get the global database instance.
    
    Args:
        db_path: Optional database path for initialization
        
    Returns:
        Database instance
    """
    global _db_instance
    if _db_instance is None:
        _db_instance = Database(db_path)
    return _db_instance


def reset_database():
    """Reset the global database instance (mainly for testing)."""
    global _db_instance
    if _db_instance:
        _db_instance.close()
    _db_instance = None
