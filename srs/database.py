"""
Database layer for the spaced repetition system.

This module handles SQLite database operations, schema creation,
and connection management for the SRS application.
"""

import sqlite3
import logging
import threading
from pathlib import Path
from typing import Optional, List
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class Database:
    """
    SQLite database manager for the spaced repetition system.
    
    Handles database connection, schema creation, and basic operations
    with thread safety and proper error handling.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize database connection.
        
        Args:
            db_path: Path to SQLite database file. If None, uses default location.
        """
        self.db_path = db_path or self._get_default_db_path()
        self._local = threading.local()
        self._ensure_database_exists()
        
    def _get_default_db_path(self) -> str:
        """Get the default database path (~/.srs/data.db)."""
        srs_dir = Path.home() / ".srs"
        srs_dir.mkdir(exist_ok=True)
        return str(srs_dir / "data.db")
    
    def _ensure_database_exists(self):
        """Ensure database file exists and create schema if needed."""
        db_file = Path(self.db_path)
        db_file.parent.mkdir(parents=True, exist_ok=True)

        # Check if database is corrupted or needs initialization
        needs_init = False
        if not db_file.exists() or db_file.stat().st_size == 0:
            needs_init = True
            logger.info(f"Creating new database at {self.db_path}")
        else:
            # Test if database is valid
            try:
                test_conn = sqlite3.connect(self.db_path)
                test_conn.execute("SELECT name FROM sqlite_master LIMIT 1")
                test_conn.close()
                logger.info(f"Using existing database at {self.db_path}")
            except sqlite3.DatabaseError as e:
                logger.error(f"Database corruption detected: {e}")
                raise sqlite3.DatabaseError(f"Database file is corrupted: {self.db_path}")

        if needs_init:
            self._create_schema()
    
    @property
    def connection(self) -> sqlite3.Connection:
        """Get thread-local database connection."""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # Enable foreign key constraints
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            
        return self._local.connection
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions."""
        conn = self.connection
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def _create_schema(self):
        """Create database schema with tables and indexes."""
        # Create schema version table first
        version_sql = """
        CREATE TABLE IF NOT EXISTS schema_version (
            version INTEGER PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            description TEXT
        );
        """

        with self.transaction() as conn:
            conn.executescript(version_sql)

        # Check current schema version
        current_version = self._get_schema_version()

        if current_version == 0:
            # Initial schema creation (MVP)
            self._apply_migration_v1()

        # Apply any pending migrations
        self._apply_pending_migrations()

        logger.info(f"Database schema up to date (version {self._get_schema_version()})")

    def _get_schema_version(self) -> int:
        """Get current schema version."""
        try:
            result = self.execute_query("SELECT MAX(version) as version FROM schema_version")
            return result[0]['version'] if result and result[0]['version'] is not None else 0
        except sqlite3.OperationalError:
            # Table doesn't exist yet
            return 0

    def _apply_migration_v1(self):
        """Apply initial schema (MVP) - Version 1."""
        schema_sql = """
        -- Core tables for MVP
        CREATE TABLE IF NOT EXISTS decks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS cards (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            deck_id INTEGER NOT NULL,
            front TEXT NOT NULL,
            back TEXT NOT NULL,
            interval INTEGER DEFAULT 0,
            repetitions INTEGER DEFAULT 0,
            ease_factor REAL DEFAULT 2.5,
            due_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (deck_id) REFERENCES decks(id) ON DELETE CASCADE
        );

        CREATE TABLE IF NOT EXISTS reviews (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            card_id INTEGER NOT NULL,
            rating INTEGER NOT NULL,
            reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (card_id) REFERENCES cards(id) ON DELETE CASCADE
        );

        -- Performance optimization indexes
        CREATE INDEX IF NOT EXISTS idx_due_cards ON cards(due_date, deck_id);
        CREATE INDEX IF NOT EXISTS idx_deck_cards ON cards(deck_id);
        CREATE INDEX IF NOT EXISTS idx_card_reviews ON reviews(card_id);

        -- Record migration
        INSERT OR IGNORE INTO schema_version (version, description)
        VALUES (1, 'Initial MVP schema');
        """

        with self.transaction() as conn:
            conn.executescript(schema_sql)

        logger.info("Applied migration v1: Initial MVP schema")

    def _apply_pending_migrations(self):
        """Apply any pending migrations."""
        current_version = self._get_schema_version()

        # Migration v2: Tags system (Phase 1.1.1)
        if current_version < 2:
            self._apply_migration_v2()

    def _apply_migration_v2(self):
        """Apply migration v2: Tags system for Phase 1.1.1."""
        migration_sql = """
        -- Tags system tables
        CREATE TABLE IF NOT EXISTS tags (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            color TEXT DEFAULT '#808080',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS card_tags (
            card_id INTEGER NOT NULL,
            tag_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (card_id, tag_id),
            FOREIGN KEY (card_id) REFERENCES cards(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
        );

        -- Indexes for performance
        CREATE INDEX IF NOT EXISTS idx_card_tags_card_id ON card_tags(card_id);
        CREATE INDEX IF NOT EXISTS idx_card_tags_tag_id ON card_tags(tag_id);
        CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);

        -- Record migration
        INSERT INTO schema_version (version, description)
        VALUES (2, 'Phase 1.1.1: Tags system');
        """

        with self.transaction() as conn:
            conn.executescript(migration_sql)

        logger.info("Applied migration v2: Tags system")

    # Tag-related database operations
    def create_tag(self, name: str, color: str = '#808080') -> int:
        """Create a new tag and return its ID."""
        query = "INSERT INTO tags (name, color) VALUES (?, ?)"
        return self.execute_update(query, (name, color))

    def get_tag_by_name(self, name: str) -> Optional[sqlite3.Row]:
        """Get tag by name."""
        query = "SELECT * FROM tags WHERE name = ?"
        results = self.execute_query(query, (name,))
        return results[0] if results else None

    def get_tag_by_id(self, tag_id: int) -> Optional[sqlite3.Row]:
        """Get tag by ID."""
        query = "SELECT * FROM tags WHERE id = ?"
        results = self.execute_query(query, (tag_id,))
        return results[0] if results else None

    def get_all_tags(self) -> List[sqlite3.Row]:
        """Get all tags."""
        query = "SELECT * FROM tags ORDER BY name"
        return self.execute_query(query)

    def update_tag(self, tag_id: int, name: str = None, color: str = None) -> bool:
        """Update tag properties."""
        updates = []
        params = []

        if name is not None:
            updates.append("name = ?")
            params.append(name)
        if color is not None:
            updates.append("color = ?")
            params.append(color)

        if not updates:
            return False

        params.append(tag_id)
        query = f"UPDATE tags SET {', '.join(updates)} WHERE id = ?"
        return self.execute_update(query, tuple(params)) > 0

    def delete_tag(self, tag_id: int) -> bool:
        """Delete a tag and all its associations."""
        query = "DELETE FROM tags WHERE id = ?"
        return self.execute_update(query, (tag_id,)) > 0

    def add_card_tag(self, card_id: int, tag_id: int) -> bool:
        """Associate a tag with a card."""
        # Check if association already exists
        existing_query = "SELECT 1 FROM card_tags WHERE card_id = ? AND tag_id = ?"
        existing = self.execute_query(existing_query, (card_id, tag_id))
        if existing:
            return False  # Already exists

        # Add new association
        query = "INSERT INTO card_tags (card_id, tag_id) VALUES (?, ?)"
        return self.execute_update(query, (card_id, tag_id)) > 0

    def remove_card_tag(self, card_id: int, tag_id: int) -> bool:
        """Remove tag association from a card."""
        query = "DELETE FROM card_tags WHERE card_id = ? AND tag_id = ?"
        return self.execute_update(query, (card_id, tag_id)) > 0

    def get_card_tags(self, card_id: int) -> List[sqlite3.Row]:
        """Get all tags for a card."""
        query = """
        SELECT t.* FROM tags t
        JOIN card_tags ct ON t.id = ct.tag_id
        WHERE ct.card_id = ?
        ORDER BY t.name
        """
        return self.execute_query(query, (card_id,))

    def get_cards_by_tag(self, tag_id: int) -> List[sqlite3.Row]:
        """Get all cards with a specific tag."""
        query = """
        SELECT c.* FROM cards c
        JOIN card_tags ct ON c.id = ct.card_id
        WHERE ct.tag_id = ?
        ORDER BY c.created_at DESC
        """
        return self.execute_query(query, (tag_id,))

    def get_tag_usage_stats(self) -> List[sqlite3.Row]:
        """Get usage statistics for all tags."""
        query = """
        SELECT t.*, COUNT(ct.card_id) as card_count
        FROM tags t
        LEFT JOIN card_tags ct ON t.id = ct.tag_id
        GROUP BY t.id
        ORDER BY card_count DESC, t.name
        """
        return self.execute_query(query)

    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """
        Execute a SELECT query and return results.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of result rows
        """
        with self.connection as conn:
            cursor = conn.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        Execute an INSERT/UPDATE/DELETE query.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Number of affected rows or last row ID for INSERT
        """
        with self.transaction() as conn:
            cursor = conn.execute(query, params)
            return cursor.lastrowid if query.strip().upper().startswith('INSERT') else cursor.rowcount
    
    def close(self):
        """Close database connection."""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


# Global database instance
_db_instance: Optional[Database] = None


def get_database(db_path: Optional[str] = None) -> Database:
    """
    Get the global database instance.
    
    Args:
        db_path: Optional database path for initialization
        
    Returns:
        Database instance
    """
    global _db_instance
    if _db_instance is None:
        _db_instance = Database(db_path)
    return _db_instance


def reset_database():
    """Reset the global database instance (mainly for testing)."""
    global _db_instance
    if _db_instance:
        _db_instance.close()
    _db_instance = None
