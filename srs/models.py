"""
Data models for the spaced repetition system.

This module defines the core data models (Deck, Card, Review) and their
CRUD operations, validation, and relationships.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field

from .database import get_database

logger = logging.getLogger(__name__)


@dataclass
class Deck:
    """
    Represents a deck of flashcards.

    Attributes:
        id: Unique identifier (None for new decks)
        name: Deck name (must be unique)
        created_at: Creation timestamp
        daily_new_limit: Maximum new cards per day (default: 20)
        daily_review_limit: Maximum review cards per day (default: 200)
        description: Optional deck description
        settings: JSON string for additional settings
    """
    name: str
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    daily_new_limit: int = 20
    daily_review_limit: int = 200
    description: str = ""
    settings: str = "{}"
    
    def __post_init__(self):
        """Validate deck data after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Deck name cannot be empty")
        if len(self.name.strip()) > 255:
            raise ValueError("Deck name too long (max 255 characters)")
        self.name = self.name.strip()
    
    @classmethod
    def create(cls, name: str) -> 'Deck':
        """
        Create a new deck in the database.
        
        Args:
            name: Deck name
            
        Returns:
            Created Deck instance
            
        Raises:
            ValueError: If deck name is invalid or already exists
        """
        deck = cls(name=name)
        db = get_database()
        
        try:
            deck_id = db.execute_update(
                """INSERT INTO decks (name, daily_new_limit, daily_review_limit,
                   description, settings) VALUES (?, ?, ?, ?, ?)""",
                (deck.name, deck.daily_new_limit, deck.daily_review_limit,
                 deck.description, deck.settings)
            )
            deck.id = deck_id
            
            # Fetch the created_at timestamp
            result = db.execute_query(
                "SELECT created_at FROM decks WHERE id = ?",
                (deck_id,)
            )
            if result:
                deck.created_at = datetime.fromisoformat(result[0]['created_at'])
            
            logger.info(f"Created deck '{deck.name}' with ID {deck.id}")
            return deck
            
        except Exception as e:
            if "UNIQUE constraint failed" in str(e):
                raise ValueError(f"Deck '{name}' already exists")
            raise
    
    @classmethod
    def get_by_id(cls, deck_id: int) -> Optional['Deck']:
        """Get deck by ID."""
        db = get_database()
        result = db.execute_query(
            "SELECT id, name, created_at FROM decks WHERE id = ?",
            (deck_id,)
        )
        
        if result:
            row = result[0]
            return cls(
                id=row['id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
        return None
    
    @classmethod
    def get_by_name(cls, name: str) -> Optional['Deck']:
        """Get deck by name."""
        db = get_database()
        result = db.execute_query(
            """SELECT id, name, created_at, daily_new_limit, daily_review_limit,
               description, settings FROM decks WHERE name = ?""",
            (name,)
        )

        if result:
            row = result[0]
            return cls(
                id=row['id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at']),
                daily_new_limit=row['daily_new_limit'] or 20,
                daily_review_limit=row['daily_review_limit'] or 200,
                description=row['description'] or "",
                settings=row['settings'] or "{}"
            )
        return None
    
    @classmethod
    def get_all(cls) -> List['Deck']:
        """Get all decks."""
        db = get_database()
        results = db.execute_query(
            "SELECT id, name, created_at FROM decks ORDER BY name"
        )
        
        return [
            cls(
                id=row['id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
            for row in results
        ]
    
    def delete(self) -> bool:
        """
        Delete this deck and all its cards.
        
        Returns:
            True if deleted successfully
        """
        if not self.id:
            return False
        
        db = get_database()
        rows_affected = db.execute_update(
            "DELETE FROM decks WHERE id = ?",
            (self.id,)
        )
        
        if rows_affected > 0:
            logger.info(f"Deleted deck '{self.name}' (ID: {self.id})")
            return True
        return False
    
    def get_card_counts(self) -> Dict[str, int]:
        """
        Get card counts for this deck.
        
        Returns:
            Dictionary with 'total', 'due', and 'new' counts
        """
        if not self.id:
            return {'total': 0, 'due': 0, 'new': 0}
        
        db = get_database()
        
        # Total cards
        total_result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ?",
            (self.id,)
        )
        total = total_result[0]['count'] if total_result else 0
        
        # Due cards (due_date <= now)
        now_str = datetime.now().isoformat()
        due_result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ? AND due_date <= ?",
            (self.id, now_str)
        )
        due = due_result[0]['count'] if due_result else 0
        
        # New cards (repetitions = 0)
        new_result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ? AND repetitions = 0",
            (self.id,)
        )
        new = new_result[0]['count'] if new_result else 0
        
        return {'total': total, 'due': due, 'new': new}


@dataclass
class Card:
    """
    Represents a flashcard with spaced repetition scheduling.

    Attributes:
        front: Front side text
        back: Back side text
        deck_id: ID of the containing deck
        id: Unique identifier (None for new cards)
        interval: Current interval in minutes
        repetitions: Number of successful repetitions
        ease_factor: Ease factor for SM-2 algorithm
        due_date: When the card is due for review
        created_at: Creation timestamp
        difficulty: Difficulty rating (0.0-1.0, higher = more difficult)
        last_review_duration: Duration of last review in seconds
        consecutive_correct: Number of consecutive correct answers
        consecutive_incorrect: Number of consecutive incorrect answers
    """
    front: str
    back: str
    deck_id: int
    id: Optional[int] = None
    interval: int = 0
    repetitions: int = 0
    ease_factor: float = 2.5
    due_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    difficulty: float = 0.5
    last_review_duration: int = 0
    consecutive_correct: int = 0
    consecutive_incorrect: int = 0
    
    def __post_init__(self):
        """Validate card data after initialization."""
        if not self.front or not self.front.strip():
            raise ValueError("Card front cannot be empty")
        if not self.back or not self.back.strip():
            raise ValueError("Card back cannot be empty")
        
        self.front = self.front.strip()
        self.back = self.back.strip()
        
        if self.due_date is None:
            self.due_date = datetime.now()
    
    @classmethod
    def create(cls, deck_id: int, front: str, back: str) -> 'Card':
        """
        Create a new card in the database.
        
        Args:
            deck_id: ID of the deck to add the card to
            front: Front side text
            back: Back side text
            
        Returns:
            Created Card instance
        """
        card = cls(front=front, back=back, deck_id=deck_id)
        db = get_database()
        
        card_id = db.execute_update(
            """INSERT INTO cards (deck_id, front, back, interval, repetitions,
               ease_factor, due_date, difficulty, last_review_duration,
               consecutive_correct, consecutive_incorrect)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (card.deck_id, card.front, card.back, card.interval,
             card.repetitions, card.ease_factor, card.due_date.isoformat(),
             card.difficulty, card.last_review_duration,
             card.consecutive_correct, card.consecutive_incorrect)
        )
        card.id = card_id
        
        # Fetch the created_at timestamp
        result = db.execute_query(
            "SELECT created_at FROM cards WHERE id = ?",
            (card_id,)
        )
        if result:
            card.created_at = datetime.fromisoformat(result[0]['created_at'])
        
        logger.info(f"Created card in deck {deck_id}: '{card.front}' -> '{card.back}'")
        return card
    
    @classmethod
    def get_by_id(cls, card_id: int) -> Optional['Card']:
        """Get card by ID."""
        db = get_database()
        result = db.execute_query(
            """SELECT id, deck_id, front, back, interval, repetitions,
               ease_factor, due_date, created_at, difficulty, last_review_duration,
               consecutive_correct, consecutive_incorrect FROM cards WHERE id = ?""",
            (card_id,)
        )

        if result:
            row = result[0]
            return cls(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at']),
                difficulty=row['difficulty'] or 0.5,
                last_review_duration=row['last_review_duration'] or 0,
                consecutive_correct=row['consecutive_correct'] or 0,
                consecutive_incorrect=row['consecutive_incorrect'] or 0
            )
        return None
    
    @classmethod
    def get_by_deck(cls, deck_id: int) -> List['Card']:
        """Get all cards in a deck."""
        db = get_database()
        results = db.execute_query(
            """SELECT id, deck_id, front, back, interval, repetitions,
               ease_factor, due_date, created_at, difficulty, last_review_duration,
               consecutive_correct, consecutive_incorrect FROM cards
               WHERE deck_id = ? ORDER BY created_at""",
            (deck_id,)
        )

        return [
            cls(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at']),
                difficulty=row['difficulty'] or 0.5,
                last_review_duration=row['last_review_duration'] or 0,
                consecutive_correct=row['consecutive_correct'] or 0,
                consecutive_incorrect=row['consecutive_incorrect'] or 0
            )
            for row in results
        ]
    
    @classmethod
    def get_due_cards(cls, deck_id: Optional[int] = None) -> List['Card']:
        """
        Get cards that are due for review.

        Args:
            deck_id: Optional deck ID to filter by

        Returns:
            List of due cards ordered by due date
        """
        db = get_database()
        now_str = datetime.now().isoformat()

        if deck_id:
            query = """SELECT id, deck_id, front, back, interval, repetitions,
                       ease_factor, due_date, created_at, difficulty, last_review_duration,
                       consecutive_correct, consecutive_incorrect FROM cards
                       WHERE deck_id = ? AND due_date <= ?
                       ORDER BY due_date"""
            params = (deck_id, now_str)
        else:
            query = """SELECT id, deck_id, front, back, interval, repetitions,
                       ease_factor, due_date, created_at, difficulty, last_review_duration,
                       consecutive_correct, consecutive_incorrect FROM cards
                       WHERE due_date <= ?
                       ORDER BY due_date"""
            params = (now_str,)

        results = db.execute_query(query, params)

        return [
            cls(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at']),
                difficulty=row['difficulty'] or 0.5,
                last_review_duration=row['last_review_duration'] or 0,
                consecutive_correct=row['consecutive_correct'] or 0,
                consecutive_incorrect=row['consecutive_incorrect'] or 0
            )
            for row in results
        ]
    
    def save(self):
        """Save card changes to database."""
        if not self.id:
            raise ValueError("Cannot save card without ID")

        db = get_database()
        db.execute_update(
            """UPDATE cards SET front = ?, back = ?, interval = ?,
               repetitions = ?, ease_factor = ?, due_date = ?, difficulty = ?,
               last_review_duration = ?, consecutive_correct = ?,
               consecutive_incorrect = ? WHERE id = ?""",
            (self.front, self.back, self.interval, self.repetitions,
             self.ease_factor, self.due_date.isoformat(), self.difficulty,
             self.last_review_duration, self.consecutive_correct,
             self.consecutive_incorrect, self.id)
        )

    # Difficulty tracking methods
    def update_difficulty(self, rating: int, review_duration: int = 0):
        """
        Update card difficulty based on review performance.

        Args:
            rating: Review rating (1-5)
            review_duration: Time taken to review in seconds
        """
        # Update consecutive counters
        if rating >= 3:  # Correct answer
            self.consecutive_correct += 1
            self.consecutive_incorrect = 0
        else:  # Incorrect answer
            self.consecutive_incorrect += 1
            self.consecutive_correct = 0

        # Update review duration
        self.last_review_duration = review_duration

        # Calculate new difficulty based on performance
        # Difficulty ranges from 0.0 (easy) to 1.0 (very difficult)
        if rating == 1:  # Again - increase difficulty significantly
            self.difficulty = min(1.0, self.difficulty + 0.2)
        elif rating == 2:  # Hard - increase difficulty slightly
            self.difficulty = min(1.0, self.difficulty + 0.1)
        elif rating == 3:  # Good - slight decrease if difficulty is high
            if self.difficulty > 0.5:
                self.difficulty = max(0.0, self.difficulty - 0.05)
        elif rating == 4:  # Easy - decrease difficulty
            self.difficulty = max(0.0, self.difficulty - 0.1)
        elif rating == 5:  # Very easy - decrease difficulty significantly
            self.difficulty = max(0.0, self.difficulty - 0.15)

        # Factor in review duration (longer time = more difficult)
        if review_duration > 0:
            # If review took longer than 30 seconds, slightly increase difficulty
            if review_duration > 30:
                self.difficulty = min(1.0, self.difficulty + 0.02)
            # If review was very fast (< 5 seconds), slightly decrease difficulty
            elif review_duration < 5:
                self.difficulty = max(0.0, self.difficulty - 0.02)

        # Ensure difficulty stays within bounds
        self.difficulty = max(0.0, min(1.0, self.difficulty))

    def get_difficulty_level(self) -> str:
        """
        Get human-readable difficulty level.

        Returns:
            String representation of difficulty level
        """
        if self.difficulty < 0.2:
            return "Very Easy"
        elif self.difficulty < 0.4:
            return "Easy"
        elif self.difficulty < 0.6:
            return "Medium"
        elif self.difficulty < 0.8:
            return "Hard"
        else:
            return "Very Hard"

    def is_difficult(self) -> bool:
        """
        Check if card is considered difficult.

        Returns:
            True if difficulty is above 0.6
        """
        return self.difficulty > 0.6

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for this card.

        Returns:
            Dictionary with performance metrics
        """
        total_reviews = self.repetitions
        if total_reviews == 0:
            success_rate = 0.0
        else:
            # Estimate success rate based on consecutive correct vs total repetitions
            success_rate = max(0.0, min(1.0, self.consecutive_correct / max(1, total_reviews)))

        return {
            'difficulty': self.difficulty,
            'difficulty_level': self.get_difficulty_level(),
            'consecutive_correct': self.consecutive_correct,
            'consecutive_incorrect': self.consecutive_incorrect,
            'success_rate': success_rate,
            'last_review_duration': self.last_review_duration,
            'is_difficult': self.is_difficult(),
            'total_repetitions': self.repetitions
        }

    # Tag-related methods
    def get_tags(self) -> List['Tag']:
        """
        Get all tags associated with this card.

        Returns:
            List of Tag instances
        """
        if self.id is None:
            return []

        db = get_database()
        tag_rows = db.get_card_tags(self.id)

        return [
            Tag(
                id=row['id'],
                name=row['name'],
                color=row['color'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
            for row in tag_rows
        ]

    def add_tag(self, tag: 'Tag') -> bool:
        """
        Add a tag to this card.

        Args:
            tag: Tag instance to add

        Returns:
            True if tag was added successfully
        """
        if self.id is None or tag.id is None:
            return False

        db = get_database()
        return db.add_card_tag(self.id, tag.id)

    def add_tag_by_name(self, tag_name: str) -> bool:
        """
        Add a tag to this card by tag name.

        Args:
            tag_name: Name of the tag to add

        Returns:
            True if tag was added successfully
        """
        tag = Tag.get_by_name(tag_name)
        if not tag:
            return False

        return self.add_tag(tag)

    def remove_tag(self, tag: 'Tag') -> bool:
        """
        Remove a tag from this card.

        Args:
            tag: Tag instance to remove

        Returns:
            True if tag was removed successfully
        """
        if self.id is None or tag.id is None:
            return False

        db = get_database()
        return db.remove_card_tag(self.id, tag.id)

    def remove_tag_by_name(self, tag_name: str) -> bool:
        """
        Remove a tag from this card by tag name.

        Args:
            tag_name: Name of the tag to remove

        Returns:
            True if tag was removed successfully
        """
        tag = Tag.get_by_name(tag_name)
        if not tag:
            return False

        return self.remove_tag(tag)

    def has_tag(self, tag: 'Tag') -> bool:
        """
        Check if this card has a specific tag.

        Args:
            tag: Tag instance to check

        Returns:
            True if card has the tag
        """
        if self.id is None or tag.id is None:
            return False

        tags = self.get_tags()
        return any(t.id == tag.id for t in tags)

    def has_tag_by_name(self, tag_name: str) -> bool:
        """
        Check if this card has a tag by name.

        Args:
            tag_name: Name of the tag to check

        Returns:
            True if card has the tag
        """
        tags = self.get_tags()
        return any(t.name.lower() == tag_name.lower() for t in tags)


@dataclass
class Review:
    """
    Represents a review session record.
    
    Attributes:
        card_id: ID of the reviewed card
        rating: Review rating (1-4)
        reviewed_at: Review timestamp
        id: Unique identifier (None for new reviews)
    """
    card_id: int
    rating: int
    reviewed_at: Optional[datetime] = None
    id: Optional[int] = None
    
    def __post_init__(self):
        """Validate review data after initialization."""
        if self.rating not in [1, 2, 3, 4]:
            raise ValueError("Rating must be 1, 2, 3, or 4")
        
        if self.reviewed_at is None:
            self.reviewed_at = datetime.now()
    
    @classmethod
    def create(cls, card_id: int, rating: int) -> 'Review':
        """
        Create a new review record.
        
        Args:
            card_id: ID of the reviewed card
            rating: Review rating (1-4)
            
        Returns:
            Created Review instance
        """
        review = cls(card_id=card_id, rating=rating)
        db = get_database()
        
        review_id = db.execute_update(
            "INSERT INTO reviews (card_id, rating, reviewed_at) VALUES (?, ?, ?)",
            (review.card_id, review.rating, review.reviewed_at.isoformat())
        )
        review.id = review_id
        
        logger.info(f"Created review for card {card_id} with rating {rating}")
        return review
    
    @classmethod
    def get_by_card(cls, card_id: int) -> List['Review']:
        """Get all reviews for a card."""
        db = get_database()
        results = db.execute_query(
            """SELECT id, card_id, rating, reviewed_at FROM reviews 
               WHERE card_id = ? ORDER BY reviewed_at DESC""",
            (card_id,)
        )
        
        return [
            cls(
                id=row['id'],
                card_id=row['card_id'],
                rating=row['rating'],
                reviewed_at=datetime.fromisoformat(row['reviewed_at'])
            )
            for row in results
        ]


@dataclass
class Tag:
    """
    Represents a tag for organizing cards.

    Attributes:
        id: Unique identifier (None for new tags)
        name: Tag name (must be unique)
        color: Hex color code for display
        created_at: Creation timestamp
    """
    name: str
    color: str = '#808080'
    id: Optional[int] = None
    created_at: Optional[datetime] = None

    def __post_init__(self):
        """Validate tag data after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Tag name cannot be empty")

        # Normalize name (strip whitespace, convert to lowercase for consistency)
        self.name = self.name.strip()

        # Validate color format (basic hex color validation)
        if not self.color.startswith('#') or len(self.color) != 7:
            self.color = '#808080'  # Default to gray if invalid

    @classmethod
    def create(cls, name: str, color: str = '#808080') -> 'Tag':
        """
        Create a new tag in the database.

        Args:
            name: Tag name (must be unique)
            color: Hex color code for display

        Returns:
            Tag instance with assigned ID

        Raises:
            ValueError: If tag name is invalid or already exists
        """
        # Validate input
        if not name or not name.strip():
            raise ValueError("Tag name cannot be empty")

        name = name.strip()

        # Check if tag already exists
        db = get_database()
        existing = db.get_tag_by_name(name)
        if existing:
            raise ValueError(f"Tag '{name}' already exists")

        # Create tag
        tag_id = db.create_tag(name, color)

        # Return new tag instance
        tag_row = db.get_tag_by_id(tag_id)
        return cls(
            id=tag_row['id'],
            name=tag_row['name'],
            color=tag_row['color'],
            created_at=datetime.fromisoformat(tag_row['created_at'])
        )

    @classmethod
    def get_by_id(cls, tag_id: int) -> Optional['Tag']:
        """
        Get tag by ID.

        Args:
            tag_id: Tag ID

        Returns:
            Tag instance or None if not found
        """
        db = get_database()
        row = db.get_tag_by_id(tag_id)

        if not row:
            return None

        return cls(
            id=row['id'],
            name=row['name'],
            color=row['color'],
            created_at=datetime.fromisoformat(row['created_at'])
        )

    @classmethod
    def get_by_name(cls, name: str) -> Optional['Tag']:
        """
        Get tag by name.

        Args:
            name: Tag name

        Returns:
            Tag instance or None if not found
        """
        db = get_database()
        row = db.get_tag_by_name(name.strip())

        if not row:
            return None

        return cls(
            id=row['id'],
            name=row['name'],
            color=row['color'],
            created_at=datetime.fromisoformat(row['created_at'])
        )

    @classmethod
    def get_all(cls) -> List['Tag']:
        """
        Get all tags.

        Returns:
            List of Tag instances
        """
        db = get_database()
        results = db.get_all_tags()

        return [
            cls(
                id=row['id'],
                name=row['name'],
                color=row['color'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
            for row in results
        ]

    @classmethod
    def get_usage_stats(cls) -> List[Dict[str, Any]]:
        """
        Get usage statistics for all tags.

        Returns:
            List of dictionaries with tag info and usage counts
        """
        db = get_database()
        results = db.get_tag_usage_stats()

        return [
            {
                'tag': cls(
                    id=row['id'],
                    name=row['name'],
                    color=row['color'],
                    created_at=datetime.fromisoformat(row['created_at'])
                ),
                'card_count': row['card_count']
            }
            for row in results
        ]

    def update(self, name: str = None, color: str = None) -> bool:
        """
        Update tag properties.

        Args:
            name: New tag name (optional)
            color: New color (optional)

        Returns:
            True if update was successful

        Raises:
            ValueError: If tag name is invalid or already exists
        """
        if self.id is None:
            raise ValueError("Cannot update tag without ID")

        # Validate new name if provided
        if name is not None:
            name = name.strip()
            if not name:
                raise ValueError("Tag name cannot be empty")

            # Check if new name conflicts with existing tag
            if name != self.name:
                db = get_database()
                existing = db.get_tag_by_name(name)
                if existing and existing['id'] != self.id:
                    raise ValueError(f"Tag '{name}' already exists")

        # Update in database
        db = get_database()
        success = db.update_tag(self.id, name, color)

        if success:
            # Update local instance
            if name is not None:
                self.name = name
            if color is not None:
                self.color = color

        return success

    def delete(self) -> bool:
        """
        Delete this tag and all its associations.

        Returns:
            True if deletion was successful
        """
        if self.id is None:
            return False

        db = get_database()
        return db.delete_tag(self.id)

    def get_cards(self) -> List['Card']:
        """
        Get all cards associated with this tag.

        Returns:
            List of Card instances
        """
        if self.id is None:
            return []

        db = get_database()
        card_rows = db.get_cards_by_tag(self.id)

        return [
            Card(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at']),
                difficulty=row['difficulty'] or 0.5,
                last_review_duration=row['last_review_duration'] or 0,
                consecutive_correct=row['consecutive_correct'] or 0,
                consecutive_incorrect=row['consecutive_incorrect'] or 0
            )
            for row in card_rows
        ]
