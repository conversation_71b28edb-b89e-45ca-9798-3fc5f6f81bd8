"""
Data models for the spaced repetition system.

This module defines the core data models (Deck, Card, Review) and their
CRUD operations, validation, and relationships.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field

from .database import get_database

logger = logging.getLogger(__name__)


@dataclass
class Deck:
    """
    Represents a deck of flashcards.
    
    Attributes:
        id: Unique identifier (None for new decks)
        name: Deck name (must be unique)
        created_at: Creation timestamp
    """
    name: str
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate deck data after initialization."""
        if not self.name or not self.name.strip():
            raise ValueError("Deck name cannot be empty")
        if len(self.name.strip()) > 255:
            raise ValueError("Deck name too long (max 255 characters)")
        self.name = self.name.strip()
    
    @classmethod
    def create(cls, name: str) -> 'Deck':
        """
        Create a new deck in the database.
        
        Args:
            name: Deck name
            
        Returns:
            Created Deck instance
            
        Raises:
            ValueError: If deck name is invalid or already exists
        """
        deck = cls(name=name)
        db = get_database()
        
        try:
            deck_id = db.execute_update(
                "INSERT INTO decks (name) VALUES (?)",
                (deck.name,)
            )
            deck.id = deck_id
            
            # Fetch the created_at timestamp
            result = db.execute_query(
                "SELECT created_at FROM decks WHERE id = ?",
                (deck_id,)
            )
            if result:
                deck.created_at = datetime.fromisoformat(result[0]['created_at'])
            
            logger.info(f"Created deck '{deck.name}' with ID {deck.id}")
            return deck
            
        except Exception as e:
            if "UNIQUE constraint failed" in str(e):
                raise ValueError(f"Deck '{name}' already exists")
            raise
    
    @classmethod
    def get_by_id(cls, deck_id: int) -> Optional['Deck']:
        """Get deck by ID."""
        db = get_database()
        result = db.execute_query(
            "SELECT id, name, created_at FROM decks WHERE id = ?",
            (deck_id,)
        )
        
        if result:
            row = result[0]
            return cls(
                id=row['id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
        return None
    
    @classmethod
    def get_by_name(cls, name: str) -> Optional['Deck']:
        """Get deck by name."""
        db = get_database()
        result = db.execute_query(
            "SELECT id, name, created_at FROM decks WHERE name = ?",
            (name,)
        )
        
        if result:
            row = result[0]
            return cls(
                id=row['id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
        return None
    
    @classmethod
    def get_all(cls) -> List['Deck']:
        """Get all decks."""
        db = get_database()
        results = db.execute_query(
            "SELECT id, name, created_at FROM decks ORDER BY name"
        )
        
        return [
            cls(
                id=row['id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at'])
            )
            for row in results
        ]
    
    def delete(self) -> bool:
        """
        Delete this deck and all its cards.
        
        Returns:
            True if deleted successfully
        """
        if not self.id:
            return False
        
        db = get_database()
        rows_affected = db.execute_update(
            "DELETE FROM decks WHERE id = ?",
            (self.id,)
        )
        
        if rows_affected > 0:
            logger.info(f"Deleted deck '{self.name}' (ID: {self.id})")
            return True
        return False
    
    def get_card_counts(self) -> Dict[str, int]:
        """
        Get card counts for this deck.
        
        Returns:
            Dictionary with 'total', 'due', and 'new' counts
        """
        if not self.id:
            return {'total': 0, 'due': 0, 'new': 0}
        
        db = get_database()
        
        # Total cards
        total_result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ?",
            (self.id,)
        )
        total = total_result[0]['count'] if total_result else 0
        
        # Due cards (due_date <= now)
        now_str = datetime.now().isoformat()
        due_result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ? AND due_date <= ?",
            (self.id, now_str)
        )
        due = due_result[0]['count'] if due_result else 0
        
        # New cards (repetitions = 0)
        new_result = db.execute_query(
            "SELECT COUNT(*) as count FROM cards WHERE deck_id = ? AND repetitions = 0",
            (self.id,)
        )
        new = new_result[0]['count'] if new_result else 0
        
        return {'total': total, 'due': due, 'new': new}


@dataclass
class Card:
    """
    Represents a flashcard with spaced repetition scheduling.
    
    Attributes:
        front: Front side text
        back: Back side text
        deck_id: ID of the containing deck
        id: Unique identifier (None for new cards)
        interval: Current interval in minutes
        repetitions: Number of successful repetitions
        ease_factor: Ease factor for SM-2 algorithm
        due_date: When the card is due for review
        created_at: Creation timestamp
    """
    front: str
    back: str
    deck_id: int
    id: Optional[int] = None
    interval: int = 0
    repetitions: int = 0
    ease_factor: float = 2.5
    due_date: Optional[datetime] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate card data after initialization."""
        if not self.front or not self.front.strip():
            raise ValueError("Card front cannot be empty")
        if not self.back or not self.back.strip():
            raise ValueError("Card back cannot be empty")
        
        self.front = self.front.strip()
        self.back = self.back.strip()
        
        if self.due_date is None:
            self.due_date = datetime.now()
    
    @classmethod
    def create(cls, deck_id: int, front: str, back: str) -> 'Card':
        """
        Create a new card in the database.
        
        Args:
            deck_id: ID of the deck to add the card to
            front: Front side text
            back: Back side text
            
        Returns:
            Created Card instance
        """
        card = cls(front=front, back=back, deck_id=deck_id)
        db = get_database()
        
        card_id = db.execute_update(
            """INSERT INTO cards (deck_id, front, back, interval, repetitions, 
               ease_factor, due_date) VALUES (?, ?, ?, ?, ?, ?, ?)""",
            (card.deck_id, card.front, card.back, card.interval, 
             card.repetitions, card.ease_factor, card.due_date.isoformat())
        )
        card.id = card_id
        
        # Fetch the created_at timestamp
        result = db.execute_query(
            "SELECT created_at FROM cards WHERE id = ?",
            (card_id,)
        )
        if result:
            card.created_at = datetime.fromisoformat(result[0]['created_at'])
        
        logger.info(f"Created card in deck {deck_id}: '{card.front}' -> '{card.back}'")
        return card
    
    @classmethod
    def get_by_id(cls, card_id: int) -> Optional['Card']:
        """Get card by ID."""
        db = get_database()
        result = db.execute_query(
            """SELECT id, deck_id, front, back, interval, repetitions, 
               ease_factor, due_date, created_at FROM cards WHERE id = ?""",
            (card_id,)
        )
        
        if result:
            row = result[0]
            return cls(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at'])
            )
        return None
    
    @classmethod
    def get_by_deck(cls, deck_id: int) -> List['Card']:
        """Get all cards in a deck."""
        db = get_database()
        results = db.execute_query(
            """SELECT id, deck_id, front, back, interval, repetitions, 
               ease_factor, due_date, created_at FROM cards 
               WHERE deck_id = ? ORDER BY created_at""",
            (deck_id,)
        )
        
        return [
            cls(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at'])
            )
            for row in results
        ]
    
    @classmethod
    def get_due_cards(cls, deck_id: Optional[int] = None) -> List['Card']:
        """
        Get cards that are due for review.
        
        Args:
            deck_id: Optional deck ID to filter by
            
        Returns:
            List of due cards ordered by due date
        """
        db = get_database()
        
        if deck_id:
            query = """SELECT id, deck_id, front, back, interval, repetitions, 
                       ease_factor, due_date, created_at FROM cards 
                       WHERE deck_id = ? AND due_date <= datetime('now')
                       ORDER BY due_date"""
            params = (deck_id,)
        else:
            query = """SELECT id, deck_id, front, back, interval, repetitions, 
                       ease_factor, due_date, created_at FROM cards 
                       WHERE due_date <= datetime('now')
                       ORDER BY due_date"""
            params = ()
        
        results = db.execute_query(query, params)
        
        return [
            cls(
                id=row['id'],
                deck_id=row['deck_id'],
                front=row['front'],
                back=row['back'],
                interval=row['interval'],
                repetitions=row['repetitions'],
                ease_factor=row['ease_factor'],
                due_date=datetime.fromisoformat(row['due_date']),
                created_at=datetime.fromisoformat(row['created_at'])
            )
            for row in results
        ]
    
    def save(self):
        """Save card changes to database."""
        if not self.id:
            raise ValueError("Cannot save card without ID")
        
        db = get_database()
        db.execute_update(
            """UPDATE cards SET front = ?, back = ?, interval = ?, 
               repetitions = ?, ease_factor = ?, due_date = ? WHERE id = ?""",
            (self.front, self.back, self.interval, self.repetitions,
             self.ease_factor, self.due_date.isoformat(), self.id)
        )


@dataclass
class Review:
    """
    Represents a review session record.
    
    Attributes:
        card_id: ID of the reviewed card
        rating: Review rating (1-4)
        reviewed_at: Review timestamp
        id: Unique identifier (None for new reviews)
    """
    card_id: int
    rating: int
    reviewed_at: Optional[datetime] = None
    id: Optional[int] = None
    
    def __post_init__(self):
        """Validate review data after initialization."""
        if self.rating not in [1, 2, 3, 4]:
            raise ValueError("Rating must be 1, 2, 3, or 4")
        
        if self.reviewed_at is None:
            self.reviewed_at = datetime.now()
    
    @classmethod
    def create(cls, card_id: int, rating: int) -> 'Review':
        """
        Create a new review record.
        
        Args:
            card_id: ID of the reviewed card
            rating: Review rating (1-4)
            
        Returns:
            Created Review instance
        """
        review = cls(card_id=card_id, rating=rating)
        db = get_database()
        
        review_id = db.execute_update(
            "INSERT INTO reviews (card_id, rating, reviewed_at) VALUES (?, ?, ?)",
            (review.card_id, review.rating, review.reviewed_at.isoformat())
        )
        review.id = review_id
        
        logger.info(f"Created review for card {card_id} with rating {rating}")
        return review
    
    @classmethod
    def get_by_card(cls, card_id: int) -> List['Review']:
        """Get all reviews for a card."""
        db = get_database()
        results = db.execute_query(
            """SELECT id, card_id, rating, reviewed_at FROM reviews 
               WHERE card_id = ? ORDER BY reviewed_at DESC""",
            (card_id,)
        )
        
        return [
            cls(
                id=row['id'],
                card_id=row['card_id'],
                rating=row['rating'],
                reviewed_at=datetime.fromisoformat(row['reviewed_at'])
            )
            for row in results
        ]
