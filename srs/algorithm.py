"""
SM-2 spaced repetition algorithm implementation.

This module implements the SM-2 algorithm for calculating optimal
review intervals based on user performance ratings.
"""

import logging
import random
from datetime import datetime, timedelta
from typing import NamedTuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


# SM-2 Algorithm Configuration
@dataclass
class SM2Config:
    """Configuration parameters for the SM-2 algorithm."""
    initial_ease_factor: float = 2.5
    min_ease_factor: float = 1.3
    max_ease_factor: float = 2.5
    learning_steps: list = None  # [1, 10] minutes
    min_interval: int = 1  # day
    max_interval: int = 365  # days
    easy_bonus: float = 1.3
    fuzz_range: float = 0.05  # ±5%
    
    def __post_init__(self):
        if self.learning_steps is None:
            self.learning_steps = [1, 10]  # minutes


class ReviewResult(NamedTuple):
    """Result of a card review with updated scheduling parameters."""
    interval: int  # New interval in minutes
    repetitions: int  # Updated repetition count
    ease_factor: float  # Updated ease factor
    due_date: datetime  # Next due date


class SM2Algorithm:
    """
    Implementation of the SM-2 spaced repetition algorithm.
    
    The SM-2 algorithm calculates optimal review intervals based on:
    - User performance rating (1-4)
    - Current ease factor
    - Number of successful repetitions
    - Learning steps for new cards
    """
    
    def __init__(self, config: SM2Config = None):
        """
        Initialize the SM-2 algorithm.
        
        Args:
            config: Algorithm configuration parameters
        """
        self.config = config or SM2Config()
        logger.info("SM-2 algorithm initialized with config: %s", self.config)
    
    def review_card(self, card, rating: int) -> ReviewResult:
        """
        Process a card review and calculate new scheduling parameters.
        
        Args:
            card: Card object with current scheduling data
            rating: User rating (1=Again, 2=Hard, 3=Good, 4=Easy)
            
        Returns:
            ReviewResult with updated scheduling parameters
        """
        if rating not in [1, 2, 3, 4]:
            raise ValueError("Rating must be 1, 2, 3, or 4")
        
        logger.debug(f"Reviewing card {card.id} with rating {rating}")
        logger.debug(f"Current state: interval={card.interval}, reps={card.repetitions}, ef={card.ease_factor}")
        
        # Handle new cards (in learning phase)
        if card.repetitions == 0 or card.interval < self.config.min_interval * 24 * 60:
            return self._handle_learning_card(card, rating)
        
        # Handle graduated cards (using SM-2)
        return self._handle_graduated_card(card, rating)
    
    def _handle_learning_card(self, card, rating: int) -> ReviewResult:
        """
        Handle cards in the learning phase (new cards).
        
        Learning steps: 1 min, 10 min, then graduate to 1 day
        """
        learning_steps = self.config.learning_steps
        
        if rating == 1:  # Again - restart learning
            interval = learning_steps[0]
            repetitions = 0
            ease_factor = card.ease_factor
            logger.debug(f"Learning card failed, restarting at {interval} minutes")
            
        elif rating in [2, 3, 4]:  # Hard, Good, Easy - advance
            if card.interval == 0:
                # New card: 0 -> first learning step (1 min)
                interval = learning_steps[0]
                repetitions = 0
                ease_factor = card.ease_factor
                logger.debug(f"New card started learning at {interval} minutes")

            elif card.interval == learning_steps[0]:
                # First step: 1 min -> 10 min
                interval = learning_steps[1] if len(learning_steps) > 1 else learning_steps[0] * 10
                repetitions = 0
                ease_factor = card.ease_factor
                logger.debug(f"Learning card advanced to step 2: {interval} minutes")

            elif card.interval == (learning_steps[1] if len(learning_steps) > 1 else learning_steps[0] * 10):
                # Second step: 10 min -> graduate to 1 day
                interval = self.config.min_interval * 24 * 60  # 1 day in minutes
                repetitions = 1
                ease_factor = self._update_ease_factor(card.ease_factor, rating)
                logger.debug(f"Learning card graduated to {interval // (24 * 60)} days")

            else:
                # Already graduated, use SM-2
                return self._handle_graduated_card(card, rating)
        
        due_date = datetime.now() + timedelta(minutes=interval)
        return ReviewResult(interval, repetitions, ease_factor, due_date)
    
    def _handle_graduated_card(self, card, rating: int) -> ReviewResult:
        """
        Handle graduated cards using the SM-2 algorithm.
        """
        if rating == 1:  # Again - back to learning
            interval = self.config.learning_steps[0]
            repetitions = 0
            ease_factor = max(card.ease_factor - 0.2, self.config.min_ease_factor)
            logger.debug(f"Graduated card failed, back to learning: {interval} minutes")
            
        else:  # Hard, Good, Easy
            ease_factor = self._update_ease_factor(card.ease_factor, rating)
            repetitions = card.repetitions + 1
            
            # Calculate base interval using SM-2 formula
            if repetitions == 1:
                base_interval = 1  # First review after graduation
            elif repetitions == 2:
                base_interval = 6  # Second review
            else:
                # SM-2 formula: I(n) = I(n-1) * EF
                previous_interval_days = card.interval / (24 * 60)  # Convert to days
                base_interval = previous_interval_days * ease_factor
            
            # Apply easy bonus for rating 4
            if rating == 4:
                base_interval *= self.config.easy_bonus
                logger.debug(f"Applied easy bonus: {self.config.easy_bonus}x")
            
            # Apply fuzz factor to prevent clustering
            base_interval = self._apply_fuzz(base_interval)
            
            # Enforce interval bounds
            base_interval = max(self.config.min_interval, min(base_interval, self.config.max_interval))
            
            interval = int(base_interval * 24 * 60)  # Convert to minutes
            logger.debug(f"Graduated card interval: {base_interval} days ({interval} minutes)")
        
        due_date = datetime.now() + timedelta(minutes=interval)
        return ReviewResult(interval, repetitions, ease_factor, due_date)
    
    def _update_ease_factor(self, current_ef: float, rating: int) -> float:
        """
        Update ease factor based on rating.
        
        SM-2 ease factor formula:
        EF' = EF + (0.1 - (5-q) * (0.08 + (5-q) * 0.02))
        where q is the rating (1-4 mapped to 0-3)
        """
        # Map rating 1-4 to quality 0-3 for SM-2 formula
        quality = rating - 1
        
        # SM-2 ease factor update formula
        new_ef = current_ef + (0.1 - (4 - quality) * (0.08 + (4 - quality) * 0.02))
        
        # Enforce bounds
        new_ef = max(self.config.min_ease_factor, min(new_ef, self.config.max_ease_factor))
        
        logger.debug(f"Ease factor updated: {current_ef:.2f} -> {new_ef:.2f} (rating {rating})")
        return new_ef
    
    def _apply_fuzz(self, interval: float) -> float:
        """
        Apply fuzz factor to interval to prevent clustering.
        
        Adds random variation of ±5% to the interval.
        """
        if interval <= 1:
            return interval  # Don't fuzz very short intervals
        
        fuzz_amount = interval * self.config.fuzz_range
        fuzz = random.uniform(-fuzz_amount, fuzz_amount)
        fuzzed_interval = interval + fuzz
        
        logger.debug(f"Applied fuzz: {interval:.2f} -> {fuzzed_interval:.2f}")
        return max(0.1, fuzzed_interval)  # Ensure positive interval


# Global algorithm instance
_algorithm_instance: SM2Algorithm = None


def get_algorithm(config: SM2Config = None) -> SM2Algorithm:
    """
    Get the global SM-2 algorithm instance.
    
    Args:
        config: Optional configuration for initialization
        
    Returns:
        SM2Algorithm instance
    """
    global _algorithm_instance
    if _algorithm_instance is None:
        _algorithm_instance = SM2Algorithm(config)
    return _algorithm_instance


def reset_algorithm():
    """Reset the global algorithm instance (mainly for testing)."""
    global _algorithm_instance
    _algorithm_instance = None
