"""
Command-line interface for the spaced repetition system.

This module implements the CLI commands for deck management, card management,
review sessions, and system utilities.
"""

import sys
import argparse
import logging
from typing import Optional, List
from pathlib import Path

from . import initialize_srs, __version__
from .models import Deck, Card
from .review import create_review_session
from .utils import (
    validate_deck_name, validate_card_content, parse_csv_file,
    format_card_counts, format_duration, truncate_text
)

logger = logging.getLogger(__name__)


class CLIError(Exception):
    """Custom exception for CLI errors."""
    pass


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up the main argument parser with all commands."""
    parser = argparse.ArgumentParser(
        prog='srs',
        description='Spaced Repetition System - A terminal-based flashcard application',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  srs create-deck "Spanish Verbs"
  srs add-card "Spanish Verbs"
  srs import-cards "Spanish Verbs" cards.csv
  srs review "Spanish Verbs"
  srs status
        """
    )

    parser.add_argument(
        '--version',
        action='version',
        version=f'SRS {__version__}'
    )

    parser.add_argument(
        '--config',
        help='Path to configuration file',
        metavar='PATH'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    # Create subparsers for commands
    subparsers = parser.add_subparsers(
        dest='command',
        help='Available commands',
        metavar='COMMAND'
    )

    # Deck management commands
    _add_deck_commands(subparsers)

    # Card management commands
    _add_card_commands(subparsers)

    # Review commands
    _add_review_commands(subparsers)

    # Utility commands
    _add_utility_commands(subparsers)

    return parser


def _add_deck_commands(subparsers):
    """Add deck management commands."""
    # create-deck command
    create_deck_parser = subparsers.add_parser(
        'create-deck',
        help='Create a new deck',
        description='Create a new flashcard deck with the specified name.'
    )
    create_deck_parser.add_argument(
        'name',
        help='Name of the deck to create'
    )

    # list-decks command
    list_decks_parser = subparsers.add_parser(
        'list-decks',
        help='List all decks',
        description='Show all decks with card counts and statistics.'
    )
    list_decks_parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='Show detailed information for each deck'
    )

    # delete-deck command
    delete_deck_parser = subparsers.add_parser(
        'delete-deck',
        help='Delete a deck and all its cards',
        description='Delete a deck and all its cards. This action cannot be undone.'
    )
    delete_deck_parser.add_argument(
        'name',
        help='Name of the deck to delete'
    )
    delete_deck_parser.add_argument(
        '--force', '-f',
        action='store_true',
        help='Skip confirmation prompt'
    )


def _add_card_commands(subparsers):
    """Add card management commands."""
    # add-card command
    add_card_parser = subparsers.add_parser(
        'add-card',
        help='Add a card to a deck',
        description='Interactively add a new flashcard to the specified deck.'
    )
    add_card_parser.add_argument(
        'deck',
        help='Name of the deck to add the card to'
    )
    add_card_parser.add_argument(
        '--front',
        help='Front side of the card (if not provided, will prompt)'
    )
    add_card_parser.add_argument(
        '--back',
        help='Back side of the card (if not provided, will prompt)'
    )

    # import-cards command
    import_cards_parser = subparsers.add_parser(
        'import-cards',
        help='Import cards from a CSV/TSV file',
        description='Import multiple cards from a CSV or TSV file.'
    )
    import_cards_parser.add_argument(
        'deck',
        help='Name of the deck to import cards into'
    )
    import_cards_parser.add_argument(
        'file',
        help='Path to CSV/TSV file containing cards'
    )
    import_cards_parser.add_argument(
        '--delimiter',
        default=',',
        help='Field delimiter (default: comma)'
    )
    import_cards_parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be imported without actually importing'
    )

    # list-cards command
    list_cards_parser = subparsers.add_parser(
        'list-cards',
        help='List cards in a deck',
        description='Show all cards in the specified deck.'
    )
    list_cards_parser.add_argument(
        'deck',
        help='Name of the deck to list cards from'
    )
    list_cards_parser.add_argument(
        '--limit', '-l',
        type=int,
        default=20,
        help='Maximum number of cards to show (default: 20)'
    )
    list_cards_parser.add_argument(
        '--due-only',
        action='store_true',
        help='Show only cards that are due for review'
    )


def _add_review_commands(subparsers):
    """Add review commands."""
    # review command
    review_parser = subparsers.add_parser(
        'review',
        help='Start a review session',
        description='Start an interactive review session for the specified deck.'
    )
    review_parser.add_argument(
        'deck',
        help='Name of the deck to review'
    )
    review_parser.add_argument(
        '--limit', '-l',
        type=int,
        help='Maximum number of cards to review'
    )


def _add_utility_commands(subparsers):
    """Add utility commands."""
    # status command
    status_parser = subparsers.add_parser(
        'status',
        help='Show system status',
        description='Show overview of all decks and due cards.'
    )
    status_parser.add_argument(
        '--detailed', '-d',
        action='store_true',
        help='Show detailed statistics'
    )


# Command implementation functions

def cmd_create_deck(args) -> int:
    """Create a new deck."""
    try:
        # Validate deck name
        is_valid, error_msg = validate_deck_name(args.name)
        if not is_valid:
            print(f"Error: {error_msg}")
            return 1

        # Create the deck
        deck = Deck.create(args.name)
        print(f"Created deck '{deck.name}' successfully.")
        return 0

    except ValueError as e:
        print(f"Error: {e}")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error creating deck: {e}")
        print(f"Error: Failed to create deck '{args.name}'")
        return 1


def cmd_list_decks(args) -> int:
    """List all decks."""
    try:
        decks = Deck.get_all()

        if not decks:
            print("No decks found. Create a deck with 'srs create-deck <name>'")
            return 0

        print(f"Found {len(decks)} deck(s):\n")

        for deck in decks:
            counts = deck.get_card_counts()
            count_str = format_card_counts(counts)

            if args.detailed:
                print(f"📚 {deck.name}")
                print(f"   Cards: {count_str}")
                print(f"   Created: {deck.created_at.strftime('%Y-%m-%d %H:%M')}")
                print()
            else:
                print(f"📚 {deck.name} - {count_str}")

        return 0

    except Exception as e:
        logger.error(f"Error listing decks: {e}")
        print("Error: Failed to list decks")
        return 1


def cmd_delete_deck(args) -> int:
    """Delete a deck and all its cards."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.name)
        if not deck:
            print(f"Error: Deck '{args.name}' not found")
            return 1

        # Get card count for confirmation
        counts = deck.get_card_counts()

        # Confirm deletion unless --force is used
        if not args.force:
            print(f"This will delete deck '{deck.name}' and all {counts['total']} cards.")
            response = input("Are you sure? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("Deletion cancelled.")
                return 0

        # Delete the deck
        if deck.delete():
            print(f"Deleted deck '{args.name}' and {counts['total']} cards.")
            return 0
        else:
            print(f"Error: Failed to delete deck '{args.name}'")
            return 1

    except Exception as e:
        logger.error(f"Error deleting deck: {e}")
        print(f"Error: Failed to delete deck '{args.name}'")
        return 1


def cmd_add_card(args) -> int:
    """Add a card to a deck."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print(f"Error: Deck '{args.deck}' not found")
            return 1

        # Get card content
        if args.front and args.back:
            front = args.front
            back = args.back
        else:
            print(f"Adding card to deck '{deck.name}'")
            front = args.front or input("Front: ").strip()
            back = args.back or input("Back: ").strip()

        # Validate card content
        is_valid, error_msg = validate_card_content(front, back)
        if not is_valid:
            print(f"Error: {error_msg}")
            return 1

        # Create the card
        Card.create(deck.id, front, back)
        print(f"Added card to '{deck.name}': {truncate_text(front)} → {truncate_text(back)}")
        return 0

    except Exception as e:
        logger.error(f"Error adding card: {e}")
        print("Error: Failed to add card")
        return 1


def cmd_import_cards(args) -> int:
    """Import cards from a CSV/TSV file."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print(f"Error: Deck '{args.deck}' not found")
            return 1

        # Check if file exists
        file_path = Path(args.file)
        if not file_path.exists():
            print(f"Error: File '{args.file}' not found")
            return 1

        # Parse the file
        try:
            cards_data = parse_csv_file(args.file, args.delimiter)
        except Exception as e:
            print(f"Error parsing file: {e}")
            return 1

        if not cards_data:
            print("No valid cards found in file")
            return 1

        # Show what will be imported
        print(f"Found {len(cards_data)} card(s) to import into '{deck.name}'")

        if args.dry_run:
            print("\nDry run - showing first 5 cards:")
            for i, card_data in enumerate(cards_data[:5]):
                print(f"  {i+1}. {truncate_text(card_data['front'])} → {truncate_text(card_data['back'])}")
            if len(cards_data) > 5:
                print(f"  ... and {len(cards_data) - 5} more")
            return 0

        # Import the cards
        imported_count = 0
        for card_data in cards_data:
            try:
                Card.create(deck.id, card_data['front'], card_data['back'])
                imported_count += 1
            except Exception as e:
                logger.warning(f"Failed to import card: {e}")

        print(f"Successfully imported {imported_count} cards into '{deck.name}'")
        if imported_count < len(cards_data):
            print(f"Warning: {len(cards_data) - imported_count} cards failed to import")

        return 0

    except Exception as e:
        logger.error(f"Error importing cards: {e}")
        print("Error: Failed to import cards")
        return 1


def cmd_list_cards(args) -> int:
    """List cards in a deck."""
    try:
        # Find the deck
        deck = Deck.get_by_name(args.deck)
        if not deck:
            print(f"Error: Deck '{args.deck}' not found")
            return 1

        # Get cards
        if args.due_only:
            cards = Card.get_due_cards(deck.id)
            card_type = "due"
        else:
            cards = Card.get_by_deck(deck.id)
            card_type = "total"

        if not cards:
            if args.due_only:
                print(f"No due cards in deck '{deck.name}'")
            else:
                print(f"No cards in deck '{deck.name}'. Add cards with 'srs add-card \"{deck.name}\"'")
            return 0

        # Apply limit
        display_cards = cards[:args.limit] if args.limit else cards

        print(f"Showing {len(display_cards)} {card_type} card(s) from '{deck.name}':")
        print()

        for i, card in enumerate(display_cards, 1):
            front = truncate_text(card.front, 40)
            back = truncate_text(card.back, 40)

            # Show scheduling info
            if card.repetitions > 0:
                interval_days = card.interval / (24 * 60)
                due_str = card.due_date.strftime('%Y-%m-%d %H:%M')
                print(f"{i:3d}. {front} → {back}")
                print(f"      Reps: {card.repetitions}, Interval: {interval_days:.1f}d, Due: {due_str}")
            else:
                print(f"{i:3d}. {front} → {back} (new)")
            print()

        if len(cards) > len(display_cards):
            print(f"... and {len(cards) - len(display_cards)} more cards")

        return 0

    except Exception as e:
        logger.error(f"Error listing cards: {e}")
        print("Error: Failed to list cards")
        return 1


def cmd_review(args) -> int:
    """Start a review session."""
    try:
        # Create review session
        session = create_review_session(args.deck)
        if not session:
            print(f"Error: Deck '{args.deck}' not found")
            return 1

        if not session.has_cards:
            print(f"No cards due for review in deck '{args.deck}'")
            return 0

        # Apply limit if specified
        if args.limit and args.limit < session.stats.total_cards:
            # Limit the cards in the session
            limited_cards = session.cards[:args.limit]
            session = create_review_session(args.deck, limited_cards)

        print(f"Starting review session for '{session.deck.name}'")
        print(f"Cards to review: {session.stats.total_cards}")
        print()

        # Review loop
        try:
            while session.has_cards:
                card = session.current_card
                progress = session.progress

                # Show progress
                print(f"[{session.deck.name}] [Card {progress['current_position']} of {progress['total_cards']}]")
                print()
                print(f"Q: {card.front}")
                print()

                # Wait for user to see answer
                input("[Press ENTER to show answer]")
                print()
                print(f"A: {card.back}")
                print()

                # Get rating
                while True:
                    print("How well did you know this?")
                    print("1) Again - Completely forgot")
                    print("2) Hard - Struggled to recall")
                    print("3) Good - Recalled with effort")
                    print("4) Easy - Instant recall")
                    print()

                    try:
                        choice = input("Your choice [1-4]: ").strip()
                        if choice in ['1', '2', '3', '4']:
                            rating = int(choice)
                            break
                        else:
                            print("Please enter 1, 2, 3, or 4")
                    except (ValueError, KeyboardInterrupt):
                        print("\nReview session interrupted.")
                        return 0

                # Review the card
                session.review_current_card(rating)
                print()
                print("-" * 50)
                print()

        except KeyboardInterrupt:
            print("\nReview session interrupted.")
            return 0

        # Show session summary
        summary = session.get_session_summary()
        print("Review session completed!")
        print()
        print(f"Cards reviewed: {summary['cards_reviewed']}")
        print(f"Duration: {format_duration(summary['duration_minutes'] * 60)}")
        print(f"Average rating: {summary['average_rating']:.1f}")
        print()
        print("Rating breakdown:")
        for rating, count in summary['rating_counts'].items():
            if count > 0:
                rating_names = {1: "Again", 2: "Hard", 3: "Good", 4: "Easy"}
                print(f"  {rating_names[rating]}: {count}")

        return 0

    except Exception as e:
        logger.error(f"Error in review session: {e}")
        print("Error: Review session failed")
        return 1


def cmd_status(args) -> int:
    """Show system status."""
    try:
        decks = Deck.get_all()

        if not decks:
            print("No decks found. Create a deck with 'srs create-deck <name>'")
            return 0

        total_due = 0
        total_new = 0
        total_cards = 0

        print("Deck Status:")
        print()

        for deck in decks:
            counts = deck.get_card_counts()
            total_due += counts['due']
            total_new += counts['new']
            total_cards += counts['total']

            count_str = format_card_counts(counts)
            print(f"📚 {deck.name}: {count_str}")

            if args.detailed and counts['total'] > 0:
                # Show breakdown
                print(f"   Total: {counts['total']}, Due: {counts['due']}, New: {counts['new']}")
                if counts['due'] > 0:
                    due_cards = Card.get_due_cards(deck.id)
                    if due_cards:
                        oldest_due = min(card.due_date for card in due_cards)
                        print(f"   Oldest due: {oldest_due.strftime('%Y-%m-%d %H:%M')}")
                print()

        print()
        print(f"Total: {total_cards} cards, {total_due} due, {total_new} new")

        if total_due > 0:
            print(f"\nStart reviewing with: srs review <deck-name>")

        return 0

    except Exception as e:
        logger.error(f"Error showing status: {e}")
        print("Error: Failed to show status")
        return 1


def cli(argv=None):
    """Main CLI entry point."""
    parser = setup_argument_parser()
    args = parser.parse_args(argv)

    # Initialize the application
    try:
        initialize_srs(args.config if hasattr(args, 'config') else None)

        # Set log level based on verbose flag
        if hasattr(args, 'verbose') and args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

    except Exception as e:
        print(f"Error: Failed to initialize SRS: {e}")
        return 1

    # Handle no command
    if not args.command:
        parser.print_help()
        return 0

    # Dispatch to command handlers
    command_handlers = {
        'create-deck': cmd_create_deck,
        'list-decks': cmd_list_decks,
        'delete-deck': cmd_delete_deck,
        'add-card': cmd_add_card,
        'import-cards': cmd_import_cards,
        'list-cards': cmd_list_cards,
        'review': cmd_review,
        'status': cmd_status,
    }

    handler = command_handlers.get(args.command)
    if handler:
        try:
            return handler(args)
        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            return 1
        except Exception as e:
            logger.error(f"Unexpected error in command {args.command}: {e}")
            print(f"Error: Command '{args.command}' failed")
            return 1
    else:
        print(f"Error: Unknown command '{args.command}'")
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(cli())
