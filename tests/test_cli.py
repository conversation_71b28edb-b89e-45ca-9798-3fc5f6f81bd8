"""
Test CLI functionality.

This module tests the command-line interface including command parsing,
validation, error handling, and all CLI commands.
"""

import pytest
import tempfile
import io
import sys
import csv
import logging
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from srs.database import reset_database, get_database
from srs.models import Deck, Card
from srs.cli import cli, setup_argument_parser
from srs.algorithm import reset_algorithm


class TestArgumentParser:
    """Test argument parser setup and command parsing."""
    
    def test_parser_setup(self):
        """Test that argument parser is set up correctly."""
        parser = setup_argument_parser()
        
        # Test basic parser properties
        assert parser.prog == 'srs'
        assert 'Spaced Repetition System' in parser.description
    
    def test_version_argument(self):
        """Test --version argument."""
        parser = setup_argument_parser()
        
        with pytest.raises(SystemExit):
            parser.parse_args(['--version'])
    
    def test_help_argument(self):
        """Test --help argument."""
        parser = setup_argument_parser()
        
        with pytest.raises(SystemExit):
            parser.parse_args(['--help'])
    
    def test_command_parsing(self):
        """Test parsing of various commands."""
        parser = setup_argument_parser()
        
        # Test create-deck command
        args = parser.parse_args(['create-deck', 'Test Deck'])
        assert args.command == 'create-deck'
        assert args.name == 'Test Deck'
        
        # Test list-decks command
        args = parser.parse_args(['list-decks'])
        assert args.command == 'list-decks'
        assert args.detailed is False
        
        args = parser.parse_args(['list-decks', '--detailed'])
        assert args.detailed is True
        
        # Test add-card command
        args = parser.parse_args(['add-card', 'Test Deck'])
        assert args.command == 'add-card'
        assert args.deck == 'Test Deck'
        
        # Test review command
        args = parser.parse_args(['review', 'Test Deck'])
        assert args.command == 'review'
        assert args.deck == 'Test Deck'
    
    def test_invalid_command(self):
        """Test parsing invalid commands."""
        parser = setup_argument_parser()
        
        with pytest.raises(SystemExit):
            parser.parse_args(['invalid-command'])


class TestCLICommands:
    """Test CLI command implementations."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        # Force the models to use our test database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
        reset_algorithm()
    
    def capture_output(self, command_args):
        """Helper to capture CLI output."""
        old_stdout = sys.stdout
        old_stderr = sys.stderr

        try:
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()
            sys.stdout = stdout_capture
            sys.stderr = stderr_capture

            # Mock initialize_srs to use our test database
            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                # Patch at the database module level since CLI imports from models
                with patch('srs.database.get_database', return_value=self.db):
                    exit_code = cli(command_args)

            return exit_code, stdout_capture.getvalue(), stderr_capture.getvalue()
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr
    
    def test_create_deck_success(self):
        """Test successful deck creation."""
        exit_code, stdout, _ = self.capture_output(['create-deck', 'Test Deck'])

        assert exit_code == 0
        assert 'Created deck' in stdout
        assert 'Test Deck' in stdout

        # Verify deck was created
        deck = Deck.get_by_name('Test Deck')
        assert deck is not None

    def test_create_deck_invalid_name(self):
        """Test deck creation with invalid name."""
        exit_code, stdout, _ = self.capture_output(['create-deck', ''])

        assert exit_code == 1
        assert 'Error:' in stdout

    def test_create_deck_duplicate(self):
        """Test creating duplicate deck."""
        # Create first deck
        Deck.create('Test Deck')

        exit_code, stdout, _ = self.capture_output(['create-deck', 'Test Deck'])

        assert exit_code == 1
        assert 'already exists' in stdout

    def test_list_decks_empty(self):
        """Test listing decks when none exist."""
        exit_code, stdout, _ = self.capture_output(['list-decks'])

        assert exit_code == 0
        assert 'No decks found' in stdout

    def test_list_decks_with_decks(self):
        """Test listing decks when some exist."""
        deck1 = Deck.create('Deck A')
        deck2 = Deck.create('Deck B')
        Card.create(deck1.id, 'front1', 'back1')
        Card.create(deck2.id, 'front2', 'back2')

        exit_code, stdout, _ = self.capture_output(['list-decks'])

        assert exit_code == 0
        assert 'Deck A' in stdout
        assert 'Deck B' in stdout
        assert '📚' in stdout

    def test_list_decks_detailed(self):
        """Test listing decks with detailed flag."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'front', 'back')

        exit_code, stdout, _ = self.capture_output(['list-decks', '--detailed'])

        assert exit_code == 0
        assert 'Test Deck' in stdout
        assert 'Created:' in stdout
    
    def test_delete_deck_success(self):
        """Test successful deck deletion with force flag."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'front', 'back')

        exit_code, stdout, _ = self.capture_output(['delete-deck', 'Test Deck', '--force'])

        assert exit_code == 0
        assert 'Deleted deck' in stdout

        # Verify deck was deleted
        assert Deck.get_by_name('Test Deck') is None

    def test_delete_deck_nonexistent(self):
        """Test deleting non-existent deck."""
        exit_code, stdout, _ = self.capture_output(['delete-deck', 'Nonexistent'])

        assert exit_code == 1
        assert 'not found' in stdout

    def test_add_card_with_arguments(self):
        """Test adding card with front and back arguments."""
        deck = Deck.create('Test Deck')

        exit_code, stdout, _ = self.capture_output([
            'add-card', 'Test Deck', '--front', 'Question', '--back', 'Answer'
        ])

        assert exit_code == 0
        assert 'Added card' in stdout

        # Verify card was created
        cards = Card.get_by_deck(deck.id)
        assert len(cards) == 1
        assert cards[0].front == 'Question'
        assert cards[0].back == 'Answer'

    def test_add_card_nonexistent_deck(self):
        """Test adding card to non-existent deck."""
        exit_code, stdout, _ = self.capture_output([
            'add-card', 'Nonexistent', '--front', 'Q', '--back', 'A'
        ])

        assert exit_code == 1
        assert 'not found' in stdout

    def test_add_card_invalid_content(self):
        """Test adding card with invalid content."""
        Deck.create('Test Deck')

        exit_code, stdout, _ = self.capture_output([
            'add-card', 'Test Deck', '--front', '', '--back', 'Answer'
        ])

        assert exit_code == 1
        assert 'Error:' in stdout
    
    def test_list_cards_empty_deck(self):
        """Test listing cards from empty deck."""
        Deck.create('Test Deck')

        exit_code, stdout, _ = self.capture_output(['list-cards', 'Test Deck'])

        assert exit_code == 0
        assert 'No cards' in stdout

    def test_list_cards_with_cards(self):
        """Test listing cards from deck with cards."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'Question 1', 'Answer 1')
        Card.create(deck.id, 'Question 2', 'Answer 2')

        exit_code, stdout, _ = self.capture_output(['list-cards', 'Test Deck'])

        assert exit_code == 0
        assert 'Question 1' in stdout
        assert 'Question 2' in stdout
        assert 'Answer 1' in stdout
        assert 'Answer 2' in stdout

    def test_list_cards_due_only(self):
        """Test listing only due cards."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'Due Card', 'Answer')

        exit_code, stdout, _ = self.capture_output(['list-cards', 'Test Deck', '--due-only'])

        assert exit_code == 0
        assert 'due card' in stdout.lower()

    def test_list_cards_with_limit(self):
        """Test listing cards with limit."""
        deck = Deck.create('Test Deck')
        for i in range(10):
            Card.create(deck.id, f'Question {i}', f'Answer {i}')

        exit_code, stdout, _ = self.capture_output(['list-cards', 'Test Deck', '--limit', '3'])

        assert exit_code == 0
        assert 'Showing 3' in stdout

    def test_status_empty(self):
        """Test status command with no decks."""
        exit_code, stdout, _ = self.capture_output(['status'])

        assert exit_code == 0
        assert 'No decks found' in stdout

    def test_status_with_decks(self):
        """Test status command with decks."""
        deck1 = Deck.create('Deck A')
        deck2 = Deck.create('Deck B')
        Card.create(deck1.id, 'front1', 'back1')
        Card.create(deck2.id, 'front2', 'back2')

        exit_code, stdout, _ = self.capture_output(['status'])

        assert exit_code == 0
        assert 'Deck Status:' in stdout
        assert 'Deck A' in stdout
        assert 'Deck B' in stdout
        assert 'Total:' in stdout

    def test_status_detailed(self):
        """Test status command with detailed flag."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'front', 'back')

        exit_code, stdout, _ = self.capture_output(['status', '--detailed'])

        assert exit_code == 0
        assert 'Total:' in stdout
        assert 'Due:' in stdout
        assert 'New:' in stdout


class TestCLIErrorHandling:
    """Test CLI error handling and edge cases."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up."""
        reset_database()
        reset_algorithm()
    
    def test_no_command(self):
        """Test CLI with no command."""
        old_stdout = sys.stdout
        try:
            stdout_capture = io.StringIO()
            sys.stdout = stdout_capture
            
            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                exit_code = cli([])
            
            output = stdout_capture.getvalue()
        finally:
            sys.stdout = old_stdout
        
        assert exit_code == 0
        assert 'usage:' in output
    
    def test_invalid_command(self):
        """Test CLI with invalid command."""
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        try:
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()
            sys.stdout = stdout_capture
            sys.stderr = stderr_capture

            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                # argparse exits with SystemExit for invalid commands
                with pytest.raises(SystemExit) as exc_info:
                    cli(['invalid-command'])

            # argparse exits with code 2 for invalid arguments
            assert exc_info.value.code == 2

            # Check that error message was printed to stderr
            stderr_output = stderr_capture.getvalue()
            assert 'invalid choice' in stderr_output

        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr
    
    def test_initialization_failure(self):
        """Test CLI when initialization fails."""
        old_stdout = sys.stdout
        try:
            stdout_capture = io.StringIO()
            sys.stdout = stdout_capture
            
            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.side_effect = Exception("Init failed")
                exit_code = cli(['status'])
            
            output = stdout_capture.getvalue()
        finally:
            sys.stdout = old_stdout
        
        assert exit_code == 1
        assert 'Failed to initialize' in output


class TestCLIInteractiveFunctionality:
    """Test CLI interactive functionality and edge cases."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()

        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up."""
        reset_database()
        reset_algorithm()

    def test_add_card_interactive_mode(self):
        """Test interactive card addition."""
        deck = Deck.create('Test Deck')

        # Mock user input
        with patch('builtins.input', side_effect=['Question?', 'Answer!']):
            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                with patch('srs.database.get_database', return_value=self.db):
                    exit_code = cli(['add-card', 'Test Deck'])

        assert exit_code == 0

        # Verify card was created
        cards = Card.get_by_deck(deck.id)
        assert len(cards) == 1
        assert cards[0].front == 'Question?'
        assert cards[0].back == 'Answer!'

    def test_delete_deck_with_confirmation(self):
        """Test deck deletion with user confirmation."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'front', 'back')

        # Test confirmation 'yes'
        with patch('builtins.input', return_value='yes'):
            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                with patch('srs.database.get_database', return_value=self.db):
                    exit_code = cli(['delete-deck', 'Test Deck'])

        assert exit_code == 0
        assert Deck.get_by_name('Test Deck') is None

    def test_delete_deck_cancelled(self):
        """Test deck deletion cancelled by user."""
        deck = Deck.create('Test Deck')
        Card.create(deck.id, 'front', 'back')

        # Test confirmation 'no'
        with patch('builtins.input', return_value='no'):
            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                with patch('srs.database.get_database', return_value=self.db):
                    exit_code = cli(['delete-deck', 'Test Deck'])

        assert exit_code == 0
        assert Deck.get_by_name('Test Deck') is not None  # Should still exist

    def test_import_cards_dry_run(self):
        """Test import cards with dry run flag."""
        deck = Deck.create('Test Deck')

        # Create test CSV file
        csv_file = Path(self.temp_dir) / "test.csv"
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['front', 'back'])
            writer.writerow(['Hello', 'Hola'])
            writer.writerow(['Goodbye', 'Adiós'])

        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['import-cards', 'Test Deck', str(csv_file), '--dry-run'])

        assert exit_code == 0

        # No cards should be imported in dry run
        cards = Card.get_by_deck(deck.id)
        assert len(cards) == 0

    def test_import_cards_actual(self):
        """Test actual card import."""
        deck = Deck.create('Test Deck')

        # Create test CSV file
        csv_file = Path(self.temp_dir) / "test.csv"
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['front', 'back'])
            writer.writerow(['Hello', 'Hola'])
            writer.writerow(['Goodbye', 'Adiós'])

        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['import-cards', 'Test Deck', str(csv_file)])

        assert exit_code == 0

        # Cards should be imported (including header row)
        cards = Card.get_by_deck(deck.id)
        assert len(cards) == 3  # Header + 2 data rows
        assert cards[0].front == 'front'  # Header row
        assert cards[1].front == 'Hello'
        assert cards[2].front == 'Goodbye'

    def test_import_cards_file_not_found(self):
        """Test import cards with non-existent file."""
        Deck.create('Test Deck')

        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['import-cards', 'Test Deck', 'nonexistent.csv'])

        assert exit_code == 1

    def test_review_command_no_due_cards(self):
        """Test review command when no cards are due."""
        deck = Deck.create('Test Deck')
        # Create card that's not due
        card = Card.create(deck.id, 'front', 'back')
        card.due_date = datetime.now() + timedelta(hours=1)
        card.save()

        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['review', 'Test Deck'])

        assert exit_code == 0

    def test_review_command_with_limit(self):
        """Test review command with card limit."""
        deck = Deck.create('Test Deck')
        # Create multiple due cards
        for i in range(5):
            Card.create(deck.id, f'front{i}', f'back{i}')

        # Mock the review session to avoid interactive input
        with patch('srs.cli.create_review_session') as mock_create_session:
            mock_session = MagicMock()
            mock_session.has_cards = False  # No cards to review
            mock_create_session.return_value = mock_session

            with patch('srs.cli.initialize_srs') as mock_init:
                mock_init.return_value = MagicMock()
                with patch('srs.database.get_database', return_value=self.db):
                    exit_code = cli(['review', 'Test Deck', '--limit', '3'])

        assert exit_code == 0

    def test_verbose_flag(self):
        """Test verbose flag functionality."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('logging.getLogger') as mock_logger:
                mock_logger_instance = MagicMock()
                mock_logger.return_value = mock_logger_instance

                exit_code = cli(['--verbose', 'status'])

                # Should set log level to DEBUG
                mock_logger_instance.setLevel.assert_called_with(logging.DEBUG)

        assert exit_code == 0

    def test_config_file_argument(self):
        """Test config file argument."""
        config_file = Path(self.temp_dir) / "test_config.json"

        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()

            exit_code = cli(['--config', str(config_file), 'status'])

            # Should pass config file to initialize_srs
            mock_init.assert_called_with(str(config_file))

        assert exit_code == 0


class TestCLIEdgeCases:
    """Test CLI edge cases and error conditions."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()

        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up."""
        reset_database()
        reset_algorithm()

    def test_keyboard_interrupt_handling(self):
        """Test handling of keyboard interrupts."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()

            # Mock a command that raises KeyboardInterrupt
            with patch('srs.cli.cmd_status', side_effect=KeyboardInterrupt):
                exit_code = cli(['status'])

        assert exit_code == 1

    def test_unexpected_exception_handling(self):
        """Test handling of unexpected exceptions."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()

            # Mock a command that raises unexpected exception
            with patch('srs.cli.cmd_status', side_effect=RuntimeError("Unexpected error")):
                exit_code = cli(['status'])

        assert exit_code == 1

    def test_command_with_unicode_arguments(self):
        """Test commands with Unicode arguments."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['create-deck', '中文测试'])

        assert exit_code == 0

        # Verify deck was created with Unicode name
        deck = Deck.get_by_name('中文测试')
        assert deck is not None

    def test_very_long_arguments(self):
        """Test commands with very long arguments."""
        long_name = "A" * 200  # Exceeds max deck name length

        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['create-deck', long_name])

        assert exit_code == 0  # Current validation allows long names

    def test_empty_arguments(self):
        """Test commands with empty arguments."""
        with patch('srs.cli.initialize_srs') as mock_init:
            mock_init.return_value = MagicMock()
            with patch('srs.database.get_database', return_value=self.db):
                exit_code = cli(['create-deck', ''])

        assert exit_code == 1  # Should fail validation


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
