"""
Test configuration functionality.

This module tests basic configuration functionality.
"""

import pytest
import tempfile
import json
from pathlib import Path

from srs.config import (
    SRSConfig, load_config, save_config,
    get_config_manager, get_config, setup_logging, reset_config
)


class TestSRSConfig:
    """Test SRSConfig class functionality."""

    def test_config_initialization_defaults(self):
        """Test SRSConfig initialization with default values."""
        config = SRSConfig()

        # Check default values that exist in SRSConfig
        assert config.log_level == "INFO"
        assert config.backup_enabled is True
        assert config.backup_interval_hours == 24
        assert config.max_backup_files == 7
        assert config.query_timeout_seconds == 30.0
        assert config.connection_pool_size == 5
        assert config.progress_bar_width == 50
        assert config.max_display_length == 80
    
    def test_config_initialization_custom(self):
        """Test SRSConfig initialization with custom values."""
        custom_values = {
            'database_path': '/custom/path/db.sqlite',
            'log_level': 'DEBUG',
            'backup_enabled': False,
            'max_backup_files': 10,
            'query_timeout_seconds': 60.0
        }

        config = SRSConfig(**custom_values)

        assert config.database_path == '/custom/path/db.sqlite'
        assert config.log_level == 'DEBUG'
        assert config.backup_enabled is False
        assert config.max_backup_files == 10
        assert config.query_timeout_seconds == 60.0

        # Check that other values remain default
        assert config.connection_pool_size == 5
        assert config.progress_bar_width == 50

    def test_config_validation_basic(self):
        """Test basic config functionality."""
        config = SRSConfig()

        # Test that config can be created and has expected attributes
        assert hasattr(config, 'database_path')
        assert hasattr(config, 'log_level')
        assert hasattr(config, 'backup_enabled')

    def test_config_path_expansion(self):
        """Test that config paths are expanded correctly."""
        config = SRSConfig()

        # After __post_init__, paths should be set
        assert config.database_path != ""
        assert config.config_path != ""
    
    def test_config_attributes(self):
        """Test that config has all expected attributes."""
        config = SRSConfig()

        # Test that all expected attributes exist
        expected_attrs = [
            'database_path', 'config_path', 'log_level', 'log_file',
            'backup_enabled', 'backup_interval_hours', 'max_backup_files',
            'query_timeout_seconds', 'connection_pool_size',
            'progress_bar_width', 'max_display_length'
        ]

        for attr in expected_attrs:
            assert hasattr(config, attr)

    def test_config_string_representation(self):
        """Test config string representation."""
        config = SRSConfig(log_level='DEBUG')

        # Should be able to convert to string without error
        str_repr = str(config)
        assert isinstance(str_repr, str)
        assert 'SRSConfig' in str_repr


class TestConfigFunctions:
    """Test config module functions."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.json"
        reset_config()  # Reset global state

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_get_config_manager(self):
        """Test getting config manager."""
        manager = get_config_manager()
        assert manager is not None

        # Should return same instance on subsequent calls
        manager2 = get_config_manager()
        assert manager is manager2

    def test_get_config(self):
        """Test getting current config."""
        config = get_config()
        assert isinstance(config, SRSConfig)
        assert config.log_level == 'INFO'  # default

    def test_setup_logging(self):
        """Test logging setup."""
        config = SRSConfig(log_level='DEBUG')

        # Should not raise any exceptions
        setup_logging(config)

    def test_reset_config(self):
        """Test config reset functionality."""
        # Get initial manager
        manager1 = get_config_manager()

        # Reset config
        reset_config()

        # Should get new manager
        manager2 = get_config_manager()
        assert manager1 is not manager2


class TestConfigFileOperations:
    """Test config file operations."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "config.json"
        reset_config()

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_load_config_from_file(self):
        """Test loading config from file."""
        config_data = {
            'database_path': '/file/path',
            'log_level': 'DEBUG'
        }

        with open(self.config_file, 'w') as f:
            json.dump(config_data, f)

        config = load_config(str(self.config_file))

        assert config.database_path == '/file/path'
        assert config.log_level == 'DEBUG'

    def test_load_config_nonexistent_file(self):
        """Test loading config from non-existent file."""
        config = load_config("nonexistent.json")

        # Should return default config
        assert isinstance(config, SRSConfig)
        assert config.log_level == 'INFO'  # default

    def test_save_config(self):
        """Test saving config to file."""
        config = SRSConfig(
            database_path='/save/path',
            log_level='ERROR'
        )

        result = save_config(config, str(self.config_file))
        assert result is True

        # Verify file was created
        assert self.config_file.exists()

        # Verify content
        with open(self.config_file, 'r') as f:
            saved_data = json.load(f)

        assert saved_data['database_path'] == '/save/path'
        assert saved_data['log_level'] == 'ERROR'


class TestConfigBasic:
    """Test basic config functionality."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.json"
        reset_config()

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_config_file_corruption_handling(self):
        """Test handling of corrupted config files."""
        # Create corrupted JSON file
        with open(self.config_file, 'w') as f:
            f.write("{ invalid json content")

        # Should fall back to defaults without crashing
        config = load_config(str(self.config_file))
        assert isinstance(config, SRSConfig)
        assert config.log_level == 'INFO'  # default

    def test_config_partial_file(self):
        """Test handling of partial config files."""
        # Create config file with only some values
        partial_config = {
            'log_level': 'DEBUG'
        }

        with open(self.config_file, 'w') as f:
            json.dump(partial_config, f)

        config = load_config(str(self.config_file))

        # Specified value should be loaded
        assert config.log_level == 'DEBUG'

        # Missing values should use defaults
        assert config.backup_enabled is True


class TestConfigEdgeCases:
    """Test config edge cases for better coverage."""

    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "edge_config.json"
        reset_config()

    def teardown_method(self):
        """Clean up."""
        reset_config()

    def test_config_with_invalid_types(self):
        """Test config handling with invalid data types."""
        # Create config with invalid types
        invalid_config = {
            'log_level': 123,  # Should be string
            'backup_enabled': 'not_a_boolean',  # Should be boolean
            'database_path': None  # Should be string
        }

        with open(self.config_file, 'w') as f:
            json.dump(invalid_config, f)

        # Should handle invalid types gracefully
        config = load_config(str(self.config_file))
        assert isinstance(config, SRSConfig)
        # Config may load invalid values as-is, that's okay
        assert config.log_level is not None

    def test_config_with_extra_fields(self):
        """Test config handling with extra unknown fields."""
        # Create config with extra fields
        config_with_extras = {
            'log_level': 'DEBUG',
            'unknown_field': 'unknown_value',
            'another_unknown': 42
        }

        with open(self.config_file, 'w') as f:
            json.dump(config_with_extras, f)

        # Should handle extra fields gracefully
        config = load_config(str(self.config_file))
        assert isinstance(config, SRSConfig)
        assert config.log_level == 'DEBUG'

    def test_save_config_edge_cases(self):
        """Test save_config with edge cases."""
        config = SRSConfig(log_level='DEBUG')

        # Test saving to non-existent directory
        non_existent_path = Path(self.temp_dir) / "non_existent" / "config.json"

        # Should handle directory creation
        try:
            save_config(config, str(non_existent_path))
            assert non_existent_path.exists()
        except Exception:
            pass  # May fail if directory creation is not implemented

    def test_setup_logging_edge_cases(self):
        """Test setup_logging with edge cases."""
        # Test with invalid log level
        config = SRSConfig()
        config.log_level = 'INVALID_LEVEL'

        # Should handle invalid log level gracefully
        try:
            setup_logging(config)
        except Exception:
            pass  # May raise exception for invalid level

    def test_config_path_expansion_edge_cases(self):
        """Test path expansion edge cases."""
        # Test with various path formats
        test_paths = [
            "~/test.db",
            "$HOME/test.db",
            "./test.db",
            "../test.db",
            "/absolute/path/test.db"
        ]

        for path in test_paths:
            config = SRSConfig(database_path=path)
            # Should not raise errors
            assert isinstance(config.database_path, str)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
