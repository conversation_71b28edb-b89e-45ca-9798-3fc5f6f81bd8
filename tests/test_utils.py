"""
Test utility functions.

This module tests utility functions that are actually implemented.
"""

import pytest
import tempfile
import csv
from pathlib import Path
from datetime import datetime

from srs.utils import (
    validate_deck_name, validate_card_content, parse_csv_file,
    format_duration, format_datetime, format_card_counts,
    truncate_text, sanitize_filename, get_file_size_mb,
    ensure_directory, clean_text
)


class TestValidationFunctions:
    """Test validation utility functions."""

    def test_validate_deck_name_basic(self):
        """Test basic deck name validation."""
        # Test valid name
        is_valid, error_msg = validate_deck_name("Test Deck")
        assert is_valid is True
        assert error_msg == ""

        # Test empty name
        is_valid, error_msg = validate_deck_name("")
        assert is_valid is False
        assert "empty" in error_msg.lower()

        # Test whitespace only
        is_valid, error_msg = validate_deck_name("   ")
        assert is_valid is False
        assert "empty" in error_msg.lower()
    
    def test_validate_card_content_basic(self):
        """Test basic card content validation."""
        # Test valid content
        is_valid, error_msg = validate_card_content("Hello", "Hola")
        assert is_valid is True
        assert error_msg == ""

        # Test empty front
        is_valid, error_msg = validate_card_content("", "back")
        assert is_valid is False
        assert "empty" in error_msg.lower()

        # Test empty back
        is_valid, error_msg = validate_card_content("front", "")
        assert is_valid is False
        assert "empty" in error_msg.lower()

        # Test whitespace only
        is_valid, error_msg = validate_card_content("   ", "back")
        assert is_valid is False
        assert "empty" in error_msg.lower()


class TestFormattingFunctions:
    """Test formatting utility functions."""

    def test_format_card_counts_basic(self):
        """Test basic card count formatting."""
        # Test empty deck
        result = format_card_counts({"total": 0, "due": 0, "new": 0})
        assert "empty" in result

        # Test deck with cards
        result = format_card_counts({"total": 5, "due": 3, "new": 2})
        assert "3" in result  # due count
        assert "2" in result  # new count
        assert isinstance(result, str)

    def test_format_duration_basic(self):
        """Test basic duration formatting."""
        # Test zero duration
        result = format_duration(0)
        assert "0" in result

        # Test short duration
        result = format_duration(30)
        assert "30" in result
        assert "second" in result

        # Test longer duration
        result = format_duration(3600)
        assert "hour" in result

    def test_format_datetime_basic(self):
        """Test basic datetime formatting."""
        dt = datetime(2023, 12, 25, 14, 30, 45)

        # Test short format
        result = format_datetime(dt, 'short')
        assert "2023" in result

        # Test that function doesn't crash with different formats
        result = format_datetime(dt, 'long')
        assert isinstance(result, str)

    def test_truncate_text_basic(self):
        """Test basic text truncation."""
        # Test short text
        result = truncate_text("Hello", 10)
        assert result == "Hello"

        # Test long text
        result = truncate_text("Hello World", 5)
        assert len(result) <= 5
        assert "..." in result

        # Test empty text
        result = truncate_text("", 10)
        assert result == ""


class TestFileOperations:
    """Test file operation utility functions."""

    def setup_method(self):
        """Set up temporary directory for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)

    def test_parse_csv_file_basic(self):
        """Test basic CSV file parsing."""
        # Create test CSV file
        csv_file = self.temp_path / "test.csv"
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(["front", "back"])
            writer.writerow(["Hello", "Hola"])

        # Parse the file
        cards = parse_csv_file(str(csv_file))

        assert len(cards) >= 1
        assert isinstance(cards, list)
        assert all(isinstance(card, dict) for card in cards)

    def test_parse_csv_file_nonexistent(self):
        """Test CSV parsing with non-existent file."""
        with pytest.raises(FileNotFoundError):
            parse_csv_file("nonexistent.csv")

    def test_sanitize_filename_basic(self):
        """Test basic filename sanitization."""
        # Test normal filename
        result = sanitize_filename("normal_file.txt")
        assert result == "normal_file.txt"

        # Test empty filename
        result = sanitize_filename("")
        assert result == "untitled"

        # Test that function returns a string
        result = sanitize_filename("test file.txt")
        assert isinstance(result, str)

    def test_get_file_size_mb_basic(self):
        """Test basic file size calculation."""
        # Create test file
        test_file = self.temp_path / "size_test.txt"
        with open(test_file, 'w') as f:
            f.write("Hello, World!")

        size_mb = get_file_size_mb(str(test_file))
        assert size_mb >= 0
        assert isinstance(size_mb, float)

        # Test non-existent file
        size_mb = get_file_size_mb("nonexistent.txt")
        assert size_mb == 0.0

    def test_ensure_directory_basic(self):
        """Test basic directory creation."""
        new_dir = self.temp_path / "new_directory"

        # Create directory
        result = ensure_directory(str(new_dir))
        assert result is True

        # Directory should exist
        assert new_dir.exists()
        assert new_dir.is_dir()


class TestTextProcessing:
    """Test text processing utility functions."""

    def test_clean_text(self):
        """Test text cleaning functionality."""
        test_cases = [
            ("  hello world  ", "hello world"),
            ("hello\n\nworld", "hello world"),
            ("hello\t\tworld", "hello world"),
            ("  multiple   spaces  ", "multiple spaces"),
            ("", ""),
            ("   ", ""),
            ("normal text", "normal text"),
            ("line1\nline2\nline3", "line1 line2 line3"),
            ("mixed\t\n  whitespace", "mixed whitespace")
        ]

        for input_text, expected in test_cases:
            result = clean_text(input_text)
            assert result == expected


class TestAdditionalUtilityFunctions:
    """Test additional utility functions for better coverage."""

    def test_validate_deck_name_edge_cases(self):
        """Test deck name validation edge cases."""
        # Test very long name
        long_name = "A" * 200
        is_valid, error_msg = validate_deck_name(long_name)
        # Should handle long names gracefully
        assert isinstance(is_valid, bool)
        assert isinstance(error_msg, str)

    def test_validate_card_content_edge_cases(self):
        """Test card content validation edge cases."""
        # Test very long content
        long_content = "A" * 10000
        is_valid, error_msg = validate_card_content(long_content, "back")
        assert isinstance(is_valid, bool)
        assert isinstance(error_msg, str)

        # Test None inputs
        try:
            is_valid, error_msg = validate_card_content(None, "back")
            assert is_valid is False
            assert "empty" in error_msg.lower()
        except (TypeError, AttributeError):
            pass  # Expected if function doesn't handle None

    def test_format_functions_edge_cases(self):
        """Test formatting functions with edge cases."""
        # Test format_card_counts with missing keys
        try:
            result = format_card_counts({})
            assert isinstance(result, str)
        except KeyError:
            pass  # Expected if function requires specific keys

        # Test format_duration with negative values
        result = format_duration(-10)
        assert isinstance(result, str)

        # Test format_duration with very large values
        result = format_duration(999999)
        assert isinstance(result, str)

    def test_truncate_text_edge_cases(self):
        """Test text truncation edge cases."""
        # Test with max_length of 0
        result = truncate_text("Hello", 0)
        assert isinstance(result, str)  # Should return some string

        # Test with negative max_length
        result = truncate_text("Hello", -1)
        assert isinstance(result, str)

        # Test with None input
        try:
            result = truncate_text(None, 10)
            assert isinstance(result, str)
        except (TypeError, AttributeError):
            pass  # Expected if function doesn't handle None

    def test_sanitize_filename_edge_cases(self):
        """Test filename sanitization edge cases."""
        # Test with None
        try:
            result = sanitize_filename(None)
            assert isinstance(result, str)
        except (TypeError, AttributeError):
            pass  # Expected if function doesn't handle None

        # Test with very long filename
        long_name = "A" * 300
        result = sanitize_filename(long_name)
        assert isinstance(result, str)

        # Test with only special characters
        result = sanitize_filename("!@#$%^&*()")
        assert isinstance(result, str)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
