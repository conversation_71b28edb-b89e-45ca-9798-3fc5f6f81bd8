"""
Test review session functionality.

This module tests the review session logic including queue management,
card ordering, progress tracking, and session state management.
"""

import pytest
import tempfile
from datetime import datetime, timedelta
from pathlib import Path

from srs.database import reset_database, get_database
from srs.models import Card, Deck, Review
from srs.review import ReviewSession, SessionStats, create_review_session
from srs.algorithm import reset_algorithm


class TestSessionStats:
    """Test SessionStats functionality."""
    
    def test_session_stats_initialization(self):
        """Test SessionStats initialization with default values."""
        stats = SessionStats()
        
        assert stats.total_cards == 0
        assert stats.cards_reviewed == 0
        assert stats.cards_remaining == 0
        assert stats.rating_counts == {1: 0, 2: 0, 3: 0, 4: 0}
        assert stats.start_time is None
        assert stats.end_time is None
    
    def test_completion_percentage_calculation(self):
        """Test completion percentage calculation."""
        stats = SessionStats()
        
        # Empty session
        assert stats.completion_percentage == 100.0
        
        # Partial completion
        stats.total_cards = 10
        stats.cards_reviewed = 3
        assert stats.completion_percentage == 30.0
        
        # Full completion
        stats.cards_reviewed = 10
        assert stats.completion_percentage == 100.0
    
    def test_duration_calculation(self):
        """Test session duration calculation."""
        stats = SessionStats()
        
        # No times set
        assert stats.duration_minutes == 0.0
        
        # With start and end times
        stats.start_time = datetime.now()
        stats.end_time = stats.start_time + timedelta(minutes=5, seconds=30)
        assert abs(stats.duration_minutes - 5.5) < 0.1


class TestReviewSession:
    """Test ReviewSession functionality."""
    
    def setup_method(self):
        """Set up test database and session for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        # Force the models to use our test database
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Test Deck")
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
        reset_algorithm()
    
    def test_session_creation_with_due_cards(self):
        """Test creating a review session with due cards."""
        # Create some due cards
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        session = ReviewSession(self.deck)
        
        assert session.deck == self.deck
        assert session.stats.total_cards == 2
        assert session.stats.cards_remaining == 2
        assert session.current_index == 0
        assert session.has_cards is True
        assert session.current_card == card1
    
    def test_session_creation_with_no_cards(self):
        """Test creating a review session with no due cards."""
        # Create cards that are not due
        card = Card.create(self.deck.id, "front", "back")
        card.due_date = datetime.now() + timedelta(hours=1)
        card.save()
        
        session = ReviewSession(self.deck)
        
        assert session.stats.total_cards == 0
        assert session.has_cards is False
        assert session.current_card is None
    
    def test_session_creation_with_specific_cards(self):
        """Test creating a review session with specific cards."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        card3 = Card.create(self.deck.id, "front3", "back3")
        
        # Create session with only specific cards
        specific_cards = [card1, card3]
        session = ReviewSession(self.deck, specific_cards)
        
        assert session.stats.total_cards == 2
        assert session.current_card == card1
    
    def test_card_ordering_by_priority(self):
        """Test that cards are ordered correctly by priority."""
        # Create cards with different priorities
        
        # Due card (past due date, has repetitions)
        due_card = Card.create(self.deck.id, "due", "back")
        due_card.due_date = datetime.now() - timedelta(hours=1)
        due_card.repetitions = 1
        due_card.save()
        
        # New card (repetitions = 0)
        new_card = Card.create(self.deck.id, "new", "back")
        new_card.due_date = datetime.now() + timedelta(hours=1)  # Future
        new_card.save()
        
        # Future card (future due date, has repetitions)
        future_card = Card.create(self.deck.id, "future", "back")
        future_card.due_date = datetime.now() + timedelta(hours=2)
        future_card.repetitions = 1
        future_card.save()
        
        # Get all cards for session
        all_cards = [due_card, new_card, future_card]
        session = ReviewSession(self.deck, all_cards)
        
        # Should be ordered: due, new, future
        assert session.cards[0].front == "due"
        assert session.cards[1].front == "new"
        assert session.cards[2].front == "future"
    
    def test_progress_tracking(self):
        """Test session progress tracking."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        session = ReviewSession(self.deck)
        
        # Initial progress
        progress = session.progress
        assert progress['current_position'] == 1
        assert progress['total_cards'] == 2
        assert progress['cards_reviewed'] == 0
        assert progress['cards_remaining'] == 2
        assert progress['completion_percentage'] == 0.0
        assert progress['deck_name'] == "Test Deck"
        
        # After reviewing one card
        session.review_current_card(3)
        progress = session.progress
        assert progress['current_position'] == 2
        assert progress['cards_reviewed'] == 1
        assert progress['cards_remaining'] == 1
        assert progress['completion_percentage'] == 50.0
    
    def test_review_current_card_success(self):
        """Test successfully reviewing the current card."""
        card = Card.create(self.deck.id, "front", "back")
        original_interval = card.interval
        
        session = ReviewSession(self.deck)
        
        # Review with rating 3
        result = session.review_current_card(3)
        
        assert result is True
        assert session.stats.cards_reviewed == 1
        assert session.stats.rating_counts[3] == 1
        assert session.current_index == 1
        assert session.has_cards is False
        
        # Check that card was updated
        updated_card = Card.get_by_id(card.id)
        assert updated_card.interval != original_interval
        
        # Check that review was recorded
        reviews = Review.get_by_card(card.id)
        assert len(reviews) == 1
        assert reviews[0].rating == 3
    
    def test_review_current_card_no_cards(self):
        """Test reviewing when no cards are available."""
        session = ReviewSession(self.deck)  # Empty deck
        
        result = session.review_current_card(3)
        assert result is False
    
    def test_skip_current_card(self):
        """Test skipping the current card."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        session = ReviewSession(self.deck)
        
        # Skip first card
        result = session.skip_current_card()
        
        assert result is True
        assert session.current_index == 1
        assert session.current_card == card2
        assert session.stats.cards_remaining == 1
        assert session.stats.cards_reviewed == 0  # Skipped, not reviewed
    
    def test_skip_current_card_no_cards(self):
        """Test skipping when no cards are available."""
        session = ReviewSession(self.deck)  # Empty deck
        
        result = session.skip_current_card()
        assert result is False
    
    def test_session_completion(self):
        """Test completing a full review session."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        session = ReviewSession(self.deck)
        
        # Review all cards
        session.review_current_card(3)
        session.review_current_card(4)
        
        # Session should be complete
        assert session.has_cards is False
        assert session.stats.cards_reviewed == 2
        assert session.stats.completion_percentage == 100.0
    
    def test_end_session(self):
        """Test ending a session and finalizing statistics."""
        card = Card.create(self.deck.id, "front", "back")
        
        session = ReviewSession(self.deck)
        session.review_current_card(3)
        
        # End session
        session.end_session()
        
        assert session.stats.end_time is not None
        assert session.stats.duration_minutes > 0
    
    def test_session_summary(self):
        """Test getting session summary."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        session = ReviewSession(self.deck)
        
        # Review cards with different ratings
        session.review_current_card(3)
        session.review_current_card(4)
        
        summary = session.get_session_summary()
        
        assert summary['deck_name'] == "Test Deck"
        assert summary['total_cards'] == 2
        assert summary['cards_reviewed'] == 2
        assert summary['completion_percentage'] == 100.0
        assert summary['rating_counts'] == {1: 0, 2: 0, 3: 1, 4: 1}
        assert summary['average_rating'] == 3.5
        assert summary['start_time'] is not None
        assert summary['end_time'] is not None
        assert summary['duration_minutes'] > 0
    
    def test_session_state_saving(self):
        """Test saving session state for resumption."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        session = ReviewSession(self.deck)
        session.review_current_card(3)  # Review first card
        
        # Save state
        state = session.save_state()
        
        assert state['deck_id'] == self.deck.id
        assert state['current_index'] == 1
        assert len(state['card_ids']) == 2
        assert state['stats']['cards_reviewed'] == 1
        assert state['stats']['rating_counts'][3] == 1
    
    def test_session_state_restoration(self):
        """Test restoring session from saved state."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        # Create and partially complete session
        original_session = ReviewSession(self.deck)
        original_session.review_current_card(3)
        state = original_session.save_state()
        
        # Restore session from state
        restored_session = ReviewSession.restore_from_state(state)
        
        assert restored_session.deck.id == self.deck.id
        assert restored_session.current_index == 1
        assert restored_session.current_card.id == card2.id
        assert restored_session.stats.cards_reviewed == 1
        assert restored_session.stats.rating_counts[3] == 1
    
    def test_session_interruption_and_resumption(self):
        """Test session interruption and resumption workflow."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        card3 = Card.create(self.deck.id, "front3", "back3")
        
        # Start session and review some cards
        session1 = ReviewSession(self.deck)
        session1.review_current_card(3)
        session1.review_current_card(2)
        
        # Save state and "interrupt" session
        state = session1.save_state()
        
        # Resume session later
        session2 = ReviewSession.restore_from_state(state)
        
        # Continue reviewing
        assert session2.current_card.id == card3.id
        session2.review_current_card(4)
        
        # Check final state
        assert session2.stats.cards_reviewed == 3
        assert session2.stats.rating_counts == {1: 0, 2: 1, 3: 1, 4: 1}
        assert session2.has_cards is False


class TestCreateReviewSession:
    """Test the create_review_session helper function."""
    
    def setup_method(self):
        """Set up test database."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Test Deck")
    
    def teardown_method(self):
        """Clean up."""
        reset_database()
        reset_algorithm()
    
    def test_create_review_session_success(self):
        """Test creating a review session with valid deck name."""
        Card.create(self.deck.id, "front", "back")
        
        session = create_review_session("Test Deck")
        
        assert session is not None
        assert session.deck.name == "Test Deck"
        assert session.stats.total_cards == 1
    
    def test_create_review_session_nonexistent_deck(self):
        """Test creating a review session with non-existent deck."""
        session = create_review_session("Nonexistent Deck")
        
        assert session is None
    
    def test_create_review_session_with_specific_cards(self):
        """Test creating a review session with specific cards."""
        card1 = Card.create(self.deck.id, "front1", "back1")
        card2 = Card.create(self.deck.id, "front2", "back2")
        
        specific_cards = [card1]
        session = create_review_session("Test Deck", specific_cards)
        
        assert session is not None
        assert session.stats.total_cards == 1
        assert session.current_card.id == card1.id


class TestReviewSessionPerformance:
    """Test review session performance requirements."""
    
    def setup_method(self):
        """Set up test database with large dataset."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        reset_algorithm()
        
        self.db = get_database(self.db_path)
        self.deck = Deck.create("Performance Test")
        
        # Create many cards
        for i in range(100):
            Card.create(self.deck.id, f"front {i}", f"back {i}")
    
    def teardown_method(self):
        """Clean up."""
        reset_database()
        reset_algorithm()
    
    def test_large_session_creation_performance(self):
        """Test that large sessions can be created quickly."""
        import time
        
        start_time = time.time()
        session = ReviewSession(self.deck)
        creation_time = time.time() - start_time
        
        assert creation_time < 1.0  # Should create in under 1 second
        assert session.stats.total_cards == 100
    
    def test_review_session_memory_usage(self):
        """Test that review sessions don't use excessive memory."""
        session = ReviewSession(self.deck)
        
        # Review all cards
        reviewed_count = 0
        while session.has_cards and reviewed_count < 100:
            session.review_current_card(3)
            reviewed_count += 1
        
        assert reviewed_count == 100
        assert session.stats.cards_reviewed == 100


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
