"""
Test card difficulty tracking functionality for Phase 1.1.2.

This module tests the difficulty tracking system, including database migration,
card difficulty updates, SM-2 algorithm integration, and performance statistics.
"""

import pytest
import tempfile
from pathlib import Path

from srs.database import reset_database
from srs.models import Card, Deck
from srs.algorithm import get_algorithm


class TestDifficultyTracking:
    """Test card difficulty tracking functionality."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_card_creation_with_difficulty_defaults(self):
        """Test that new cards have correct difficulty defaults."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        assert card.difficulty == 0.5
        assert card.last_review_duration == 0
        assert card.consecutive_correct == 0
        assert card.consecutive_incorrect == 0
    
    def test_difficulty_update_hard_rating(self):
        """Test difficulty update with hard rating (1)."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        initial_difficulty = card.difficulty
        
        card.update_difficulty(rating=1, review_duration=30)
        
        assert card.difficulty > initial_difficulty
        assert card.consecutive_incorrect == 1
        assert card.consecutive_correct == 0
        assert card.last_review_duration == 30
    
    def test_difficulty_update_easy_rating(self):
        """Test difficulty update with easy rating (4)."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        card.difficulty = 0.8  # Start with high difficulty
        
        card.update_difficulty(rating=4, review_duration=5)
        
        assert card.difficulty < 0.8
        assert card.consecutive_correct == 1
        assert card.consecutive_incorrect == 0
        assert card.last_review_duration == 5
    
    def test_difficulty_update_rating_progression(self):
        """Test difficulty changes with different rating progressions."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        # Test rating 1 (Again)
        card.update_difficulty(rating=1)
        assert card.difficulty > 0.5
        
        # Test rating 2 (Hard)
        card.difficulty = 0.5
        card.update_difficulty(rating=2)
        assert card.difficulty > 0.5
        
        # Test rating 3 (Good)
        card.difficulty = 0.8
        card.update_difficulty(rating=3)
        assert card.difficulty < 0.8
        
        # Test rating 4 (Easy)
        card.difficulty = 0.8
        card.update_difficulty(rating=4)
        assert card.difficulty < 0.8
        
        # Test rating 5 (Very Easy)
        card.difficulty = 0.8
        card.update_difficulty(rating=5)
        assert card.difficulty < 0.8
    
    def test_difficulty_bounds(self):
        """Test that difficulty stays within 0.0-1.0 bounds."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        # Test lower bound
        card.difficulty = 0.0
        card.update_difficulty(rating=4)  # Easy rating
        assert card.difficulty >= 0.0
        
        # Test upper bound
        card.difficulty = 1.0
        card.update_difficulty(rating=1)  # Hard rating
        assert card.difficulty <= 1.0
    
    def test_consecutive_counters(self):
        """Test consecutive correct/incorrect counters."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        # Test consecutive correct
        card.update_difficulty(rating=3)
        assert card.consecutive_correct == 1
        assert card.consecutive_incorrect == 0
        
        card.update_difficulty(rating=4)
        assert card.consecutive_correct == 2
        assert card.consecutive_incorrect == 0
        
        # Test reset on incorrect
        card.update_difficulty(rating=1)
        assert card.consecutive_correct == 0
        assert card.consecutive_incorrect == 1
        
        # Test consecutive incorrect
        card.update_difficulty(rating=2)
        assert card.consecutive_correct == 0
        assert card.consecutive_incorrect == 2
        
        # Test reset on correct
        card.update_difficulty(rating=3)
        assert card.consecutive_correct == 1
        assert card.consecutive_incorrect == 0
    
    def test_review_duration_impact(self):
        """Test that review duration affects difficulty."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        initial_difficulty = card.difficulty
        
        # Long review time should increase difficulty slightly
        card.update_difficulty(rating=3, review_duration=45)
        assert card.difficulty >= initial_difficulty
        
        # Reset for next test
        card.difficulty = 0.5
        
        # Fast review time should decrease difficulty slightly
        card.update_difficulty(rating=3, review_duration=3)
        assert card.difficulty <= 0.5
    
    def test_difficulty_level_classification(self):
        """Test difficulty level string classification."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        card.difficulty = 0.1
        assert card.get_difficulty_level() == "Very Easy"
        
        card.difficulty = 0.3
        assert card.get_difficulty_level() == "Easy"
        
        card.difficulty = 0.5
        assert card.get_difficulty_level() == "Medium"
        
        card.difficulty = 0.7
        assert card.get_difficulty_level() == "Hard"
        
        card.difficulty = 0.9
        assert card.get_difficulty_level() == "Very Hard"
    
    def test_is_difficult_classification(self):
        """Test is_difficult classification."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        card.difficulty = 0.5
        assert card.is_difficult() is False
        
        card.difficulty = 0.7
        assert card.is_difficult() is True
    
    def test_performance_stats(self):
        """Test performance statistics calculation."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        # Update some stats
        card.difficulty = 0.7
        card.consecutive_correct = 3
        card.consecutive_incorrect = 1
        card.last_review_duration = 25
        card.repetitions = 5
        
        stats = card.get_performance_stats()
        
        assert stats['difficulty'] == 0.7
        assert stats['difficulty_level'] == "Hard"
        assert stats['consecutive_correct'] == 3
        assert stats['consecutive_incorrect'] == 1
        assert stats['last_review_duration'] == 25
        assert stats['is_difficult'] is True
        assert stats['total_repetitions'] == 5
        assert 'success_rate' in stats
    
    def test_card_save_with_difficulty_fields(self):
        """Test that card save includes difficulty fields."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        # Update difficulty fields
        card.difficulty = 0.8
        card.last_review_duration = 30
        card.consecutive_correct = 2
        card.consecutive_incorrect = 1
        
        # Save and reload
        card.save()
        reloaded_card = Card.get_by_id(card.id)
        
        assert reloaded_card.difficulty == 0.8
        assert reloaded_card.last_review_duration == 30
        assert reloaded_card.consecutive_correct == 2
        assert reloaded_card.consecutive_incorrect == 1
    
    def test_card_loading_with_difficulty_fields(self):
        """Test that card loading includes difficulty fields."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        
        # Test get_by_id
        loaded_card = Card.get_by_id(card.id)
        assert loaded_card.difficulty == 0.5
        assert loaded_card.last_review_duration == 0
        assert loaded_card.consecutive_correct == 0
        assert loaded_card.consecutive_incorrect == 0
        
        # Test get_by_deck
        deck_cards = Card.get_by_deck(deck.id)
        assert len(deck_cards) == 1
        assert deck_cards[0].difficulty == 0.5
        
        # Test get_due_cards
        due_cards = Card.get_due_cards(deck.id)
        assert len(due_cards) == 1
        assert due_cards[0].difficulty == 0.5


class TestDifficultyDatabaseMigration:
    """Test database migration for difficulty tracking."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_schema_version_3(self):
        """Test that schema is at version 3 with difficulty tracking."""
        version = self.db._get_schema_version()
        assert version == 3
    
    def test_difficulty_columns_exist(self):
        """Test that difficulty tracking columns exist."""
        result = self.db.execute_query("PRAGMA table_info(cards)")
        columns = {row['name']: row['type'] for row in result}
        
        assert 'difficulty' in columns
        assert 'last_review_duration' in columns
        assert 'consecutive_correct' in columns
        assert 'consecutive_incorrect' in columns
    
    def test_difficulty_index_exists(self):
        """Test that difficulty index was created."""
        result = self.db.execute_query(
            "SELECT name FROM sqlite_master WHERE type='index' AND name='idx_cards_difficulty'"
        )
        assert len(result) == 1
    
    def test_migration_record_exists(self):
        """Test that migration v3 was recorded."""
        result = self.db.execute_query(
            "SELECT * FROM schema_version WHERE version = 3"
        )
        assert len(result) == 1
        assert result[0]['description'] == 'Phase 1.1.2: Card difficulty tracking'


class TestSM2AlgorithmDifficultyIntegration:
    """Test SM-2 algorithm integration with difficulty tracking."""
    
    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)
        self.algorithm = get_algorithm()
    
    def teardown_method(self):
        """Clean up after each test."""
        reset_database()
    
    def test_difficulty_adjustment_easy_card(self):
        """Test that easy cards get longer intervals."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        card.difficulty = 0.1  # Very easy
        card.repetitions = 2  # Graduated card
        card.interval = 1440  # 1 day in minutes
        
        result = self.algorithm.review_card(card, 3)
        
        # Easy cards should get longer intervals
        assert result.interval > 1440
    
    def test_difficulty_adjustment_hard_card(self):
        """Test that hard cards get shorter intervals."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        card.difficulty = 0.9  # Very hard
        card.repetitions = 2  # Graduated card
        card.interval = 1440  # 1 day in minutes
        
        result = self.algorithm.review_card(card, 3)
        
        # Hard cards should get shorter intervals than the base calculation
        # We can't easily test the exact value without knowing the base calculation,
        # but we can test that the difficulty adjustment method is called
        assert result.interval > 0
    
    def test_consecutive_performance_adjustment(self):
        """Test that consecutive performance affects intervals."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        card.repetitions = 2  # Graduated card
        card.interval = 1440  # 1 day in minutes
        
        # Test consecutive correct answers
        card.consecutive_correct = 5
        card.consecutive_incorrect = 0
        result1 = self.algorithm.review_card(card, 3)
        
        # Reset for comparison
        card.consecutive_correct = 0
        card.consecutive_incorrect = 3
        result2 = self.algorithm.review_card(card, 3)
        
        # Cards with good consecutive performance should get longer intervals
        assert result1.interval >= result2.interval

    def test_review_duration_adjustment(self):
        """Test that review duration affects difficulty adjustment."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        card.repetitions = 2  # Graduated card
        card.interval = 1440  # 1 day in minutes

        # Test long review duration
        card.last_review_duration = 60  # 1 minute
        result1 = self.algorithm.review_card(card, 3)

        # Test short review duration
        card.last_review_duration = 3  # 3 seconds
        result2 = self.algorithm.review_card(card, 3)

        # Both should produce valid intervals
        assert result1.interval > 0
        assert result2.interval > 0


class TestDifficultyEdgeCases:
    """Test edge cases and error conditions for difficulty tracking."""

    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up after each test."""
        reset_database()

    def test_difficulty_update_invalid_rating(self):
        """Test difficulty update with invalid rating values."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        initial_difficulty = card.difficulty

        # Test with rating outside 1-5 range
        card.update_difficulty(rating=0)
        # Should not crash, but may not update difficulty in expected way
        assert card.difficulty >= 0.0

        card.update_difficulty(rating=6)
        # Should not crash, but may not update difficulty in expected way
        assert card.difficulty <= 1.0

    def test_difficulty_update_negative_duration(self):
        """Test difficulty update with negative review duration."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        # Should handle negative duration gracefully
        card.update_difficulty(rating=3, review_duration=-10)
        assert card.last_review_duration == -10  # Should store the value as-is
        assert 0.0 <= card.difficulty <= 1.0

    def test_difficulty_update_zero_duration(self):
        """Test difficulty update with zero review duration."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        card.update_difficulty(rating=3, review_duration=0)
        assert card.last_review_duration == 0
        assert 0.0 <= card.difficulty <= 1.0

    def test_performance_stats_zero_repetitions(self):
        """Test performance stats with zero repetitions."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        stats = card.get_performance_stats()
        assert stats['success_rate'] == 0.0
        assert stats['total_repetitions'] == 0

    def test_difficulty_adjustment_missing_attributes(self):
        """Test difficulty adjustment with missing card attributes."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        # Remove difficulty attribute to test fallback
        if hasattr(card, 'difficulty'):
            delattr(card, 'difficulty')

        # Algorithm should handle missing attributes gracefully
        algorithm = get_algorithm()
        result = algorithm.review_card(card, 3)
        assert result.interval > 0

    def test_card_loading_with_null_difficulty_values(self):
        """Test card loading when database has NULL difficulty values."""
        # Create card directly in database with NULL values
        deck_id = self.db.execute_update("INSERT INTO decks (name) VALUES (?)", ('Test Deck',))
        card_id = self.db.execute_update(
            """INSERT INTO cards (deck_id, front, back, difficulty, last_review_duration,
               consecutive_correct, consecutive_incorrect)
               VALUES (?, ?, ?, NULL, NULL, NULL, NULL)""",
            (deck_id, 'front', 'back')
        )

        # Loading should handle NULL values with defaults
        card = Card.get_by_id(card_id)
        assert card.difficulty == 0.5  # Default value
        assert card.last_review_duration == 0  # Default value
        assert card.consecutive_correct == 0  # Default value
        assert card.consecutive_incorrect == 0  # Default value

    def test_extreme_difficulty_values(self):
        """Test behavior with extreme difficulty values."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        # Test with difficulty at exact bounds
        card.difficulty = 0.0
        card.update_difficulty(rating=1)  # Should not go below 0
        assert card.difficulty >= 0.0

        card.difficulty = 1.0
        card.update_difficulty(rating=4)  # Should not go above 1
        assert card.difficulty <= 1.0

    def test_large_consecutive_counters(self):
        """Test behavior with very large consecutive counters."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        # Set very large consecutive counters
        card.consecutive_correct = 1000
        card.consecutive_incorrect = 0

        # Should handle large values gracefully
        algorithm = get_algorithm()
        result = algorithm.review_card(card, 3)
        assert result.interval > 0

        # Performance stats should handle large values
        stats = card.get_performance_stats()
        assert stats['consecutive_correct'] == 1000

    def test_difficulty_precision(self):
        """Test difficulty calculation precision."""
        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        # Perform many small adjustments
        for _ in range(100):
            card.update_difficulty(rating=3, review_duration=10)

        # Difficulty should remain within bounds despite many operations
        assert 0.0 <= card.difficulty <= 1.0

        # Should maintain reasonable precision
        assert isinstance(card.difficulty, float)


class TestReviewSessionDifficultyIntegration:
    """Test integration of difficulty tracking with review sessions."""

    def setup_method(self):
        """Set up test database for each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = str(Path(self.temp_dir) / "test.db")
        reset_database()
        # Force the models to use our test database
        from srs.database import get_database
        self.db = get_database(self.db_path)

    def teardown_method(self):
        """Clean up after each test."""
        reset_database()

    def test_review_session_updates_difficulty(self):
        """Test that review sessions update card difficulty."""
        from srs.review import create_review_session

        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')
        initial_difficulty = card.difficulty

        # Create review session
        session = create_review_session('Test Deck')
        assert session is not None

        # Review the card
        success = session.review_current_card(rating=1)  # Hard rating
        assert success is True

        # Reload card to check difficulty was updated
        updated_card = Card.get_by_id(card.id)
        assert updated_card.difficulty != initial_difficulty
        assert updated_card.consecutive_incorrect == 1

    def test_review_session_saves_difficulty_changes(self):
        """Test that difficulty changes are persisted to database."""
        from srs.review import create_review_session

        deck = Deck.create('Test Deck')
        card = Card.create(deck.id, 'front', 'back')

        # Create review session and review card
        session = create_review_session('Test Deck')
        session.review_current_card(rating=4)  # Easy rating

        # Create new session to verify persistence
        new_session = create_review_session('Test Deck')
        if new_session and new_session.has_cards:
            # The card should have updated difficulty from previous review
            current_card = new_session.current_card
            # Note: The card might not be the same one if there are multiple cards
            # This test verifies the persistence mechanism works
            assert current_card.difficulty >= 0.0
